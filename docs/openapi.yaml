openapi: 3.1.0
info:
  title: O6 Business Suite API
  description: |
    API specification for O6 Business Suite, covering all modules:
    - Human Capital Management (HCM)
      - Recruitment
      - HR Operations
      - Benefits
      - Learning & Development
    - Client Relationship Management (CRM)
  version: 1.0.0
  contact:
    name: O6 AI Team

servers:
- url: http://localhost:8000
  description: Local Development
- url: https://api.o6.ai
  description: Production Environment

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Forbidden:
      description: Permission denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  schemas:
    WorkspacePersona:
      type: string
      enum: [ recruiter, hr-generalist, benefits, learning ]
      description: Role-based persona for workspace access

    Program:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "prog-1"
        title:
          type: string
          example: "Q1 Marketing Campaign"
        description:
          type: string
          example: "Comprehensive marketing campaign for Q1 2024"
        quarter:
          type: string
          enum: [ Q1, Q2, Q3, Q4 ]
          example: "Q1"
        year:
          type: integer
          example: 2024
        status:
          type: string
          enum: [ planning, active, completed, on_hold ]
          example: "active"
        progress:
          type: number
          minimum: 0
          maximum: 100
          example: 75
        owner:
          type: string
          format: uuid
          example: "user-1"
        createdAt:
          type: string
          format: date-time
          example: "2024-01-01T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T12:30:00Z"
        persona:
          $ref: '#/components/schemas/WorkspacePersona'
          example: "recruiter"

    Objective:
      type: object
      properties:
        id:
          type: string
          format: uuid
        programId:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        month:
          type: integer
          minimum: 1
          maximum: 12
        year:
          type: integer
        status:
          type: string
          enum: [ not_started, in_progress, completed ]
        progress:
          type: number
          minimum: 0
          maximum: 100
        owner:
          type: string
          format: uuid
        startDate:
          type: string
          format: date
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        persona:
          $ref: '#/components/schemas/WorkspacePersona'

    Task:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "task-1"
        objectiveId:
          type: string
          format: uuid
          example: "obj-1"
        title:
          type: string
          example: "Post Senior Python Developer job on Indeed"
        description:
          type: string
          example: "Format the job description and post it on Indeed portal"
        dueDate:
          type: string
          format: date
          example: "2024-04-05"
        status:
          type: string
          enum: [ pending, in_progress, completed ]
          example: "pending"
        priority:
          type: string
          enum: [ low, medium, high ]
          example: "high"
        assignedTo:
          type: string
          format: uuid
          example: "user-1"
        relatedItems:
          type: array
          items:
            type: string
            format: uuid
          example: ["job-1", "client-1"]
        persona:
          $ref: '#/components/schemas/WorkspacePersona'
          example: "recruiter"
        type:
          type: string
          enum: [ meeting, posting, sourcing, screening, job_description, other ]
          example: "posting"
        noteId:
          type: string
          format: uuid
          example: "note-1"

    Client:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "client-1"
        orgId:
          type: string
          format: uuid
          example: "org-1"
        name:
          type: string
          example: "Acme Corporation"
        industry:
          type: string
          example: "Technology"
        location:
          type: string
          example: "San Francisco, CA"
        contactPerson:
          type: string
          example: "John Smith"
        contactEmail:
          type: string
          format: email
          example: "<EMAIL>"
        contactPhone:
          type: string
          example: "(*************"
        status:
          type: string
          enum: [ active, inactive, lead, prospect ]
          example: "active"
        notes:
          type: string
          example: "Long-term client with multiple technical positions"
        createdAt:
          type: string
          format: date-time
          example: "2023-01-15T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-06-10T00:00:00Z"
        openJobs:
          type: integer
          example: 3
        totalPlacements:
          type: integer
          example: 12

    Job:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "job-1"
        title:
          type: string
          example: "Senior Python Developer - Fintech"
        content:
          type: string
          example: "We are seeking a highly skilled and motivated Python Developer..."
        tags:
          type: array
          items:
            type: string
          example: ["Python", "Fintech", "Senior"]
        createdAt:
          type: string
          format: date-time
          example: "2023-03-15T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-03-18T00:00:00Z"
        type:
          type: string
          enum: [ job_description, boolean_search, candidate_profile, meeting_notes ]
          example: "job_description"
        status:
          type: string
          enum: [ active, inactive, draft, archived ]
          example: "active"
        category:
          type: string
          example: "Job Descriptions"
        clientId:
          type: string
          format: uuid
          example: "client-1"
        department:
          type: string
          example: "Engineering"
        location:
          type: string
          example: "San Francisco, CA"
        salary:
          type: string
          example: "$150,000 - $180,000"
        experience:
          type: string
          example: "5+ years"
        persona:
          $ref: '#/components/schemas/WorkspacePersona'
          example: "recruiter"
        versions:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              content:
                type: string
              createdAt:
                type: string
                format: date-time
          example: [
            {
              "id": "v1",
              "content": "Initial draft of Python Developer job description",
              "createdAt": "2023-03-15T00:00:00Z"
            },
            {
              "id": "v2",
              "content": "Updated Python Developer job description with additional requirements",
              "createdAt": "2023-03-18T00:00:00Z"
            }
          ]

    Note:
      oneOf:
      - $ref: '#/components/schemas/MeetingNote'
      - $ref: '#/components/schemas/PostingNote'
      - $ref: '#/components/schemas/SourcingNote'
      - $ref: '#/components/schemas/ScreeningNote'

    MeetingNote:
      type: object
      properties:
        id:
          type: string
          format: uuid
        taskId:
          type: string
          format: uuid
        type:
          type: string
          enum: [ meeting ]
        lastEdited:
          type: string
          format: date-time
        title:
          type: string
        date:
          type: string
          format: date
        attendees:
          type: array
          items:
            type: string
        roleRequirements:
          type: array
          items:
            type: string
        compensation:
          type: object
          properties:
            budget:
              type: string
            benefits:
              type: string
        timeline:
          type: object
          properties:
            startDate:
              type: string
              format: date
        nextSteps:
          type: array
          items:
            type: string

    PostingNote:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "posting-note-1"
        taskId:
          type: string
          format: uuid
          example: "task-1"
        type:
          type: string
          enum: [ posting ]
          example: "posting"
        lastEdited:
          type: string
          format: date-time
          example: "2024-01-15T14:30:00Z"
        title:
          type: string
          example: "Senior Python Developer - Fintech"
        overview:
          type: string
          example: "We are seeking a highly skilled and motivated Senior Python Developer to join our dynamic Fintech team..."
        company:
          type: string
          example: "Acme Financial"
        location:
          type: string
          example: "San Francisco, CA (Hybrid)"
        salaryRange:
          type: string
          example: "$150,000 - $180,000"
        experience:
          type: string
          example: "5+ years"
        responsibilities:
          type: array
          items:
            type: string
          example: [
            "Developing and maintaining Python-based applications for financial data processing and analysis",
            "Writing clean, efficient, and reusable code following best practices in the fintech industry",
            "Collaborating with cross-functional teams, including data scientists, product managers, and other developers"
          ]

    SourcingNote:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "sourcing-note-1"
        taskId:
          type: string
          format: uuid
          example: "task-2"
        type:
          type: string
          enum: [ sourcing ]
          example: "sourcing"
        lastEdited:
          type: string
          format: date-time
          example: "2024-01-15T15:45:00Z"
        title:
          type: string
          example: "Boolean Search - Python Developers in Bay Area"
        booleanString:
          type: string
          example: '(python OR "python developer") AND (django OR flask) AND (senior OR sr OR lead) AND ("bay area" OR san francisco OR "san jose" OR oakland) -junior -intern'
        analysis:
          type: string
          example: "This search string targets experienced Python developers in the Bay Area with experience in Django or Flask frameworks. It excludes junior positions and internships."
        expectedResults:
          type: string
          example: "Estimated 200-300 matches on LinkedIn, 150-200 on Indeed, and 100-150 on Dice."

    ScreeningNote:
      type: object
      properties:
        id:
          type: string
          format: uuid
        taskId:
          type: string
          format: uuid
        type:
          type: string
          enum: [ screening ]
        lastEdited:
          type: string
          format: date-time
        candidateName:
          type: string
        professionalSummary:
          type: string
        skills:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              rating:
                type: integer
                minimum: 1
                maximum: 5
        screeningNotes:
          type: string

    Error:
      type: object
      required: [ code, message, status ]
      properties:
        code:
          type: string
          enum:
          - VALIDATION_ERROR
          - RESOURCE_NOT_FOUND
          - UNAUTHORIZED
          - FORBIDDEN
          - CONFLICT
          - INTERNAL_ERROR
          - BAD_REQUEST
        message:
          type: string
        status:
          type: integer
        details:
          type: object
          description: Additional error details specific to the error type
          nullable: true

    PaginatedResponse:
      type: object
      required: [ data, pagination ]
      properties:
        data:
          type: array
          items:
            type: object
        pagination:
          type: object
          required: [ total, page, pageSize, totalPages ]
          properties:
            total:
              type: integer
              description: Total number of items
            page:
              type: integer
              description: Current page number (1-based)
            pageSize:
              type: integer
              description: Number of items per page
            totalPages:
              type: integer
              description: Total number of pages
            hasNext:
              type: boolean
              description: Whether there are more pages after this one
            hasPrevious:
              type: boolean
              description: Whether there are pages before this one

    WorkspaceItem:
      type: object
      properties:
        id:
          type: string
          example: "1"
        title:
          type: string
          example: "Senior Python Developer - Fintech"
        content:
          type: string
          example: "We are seeking a highly skilled and motivated Python Developer..."
        tags:
          type: array
          items:
            type: string
          example: ["Python", "Fintech", "Senior"]
        createdAt:
          type: string
          format: date-time
          example: "2023-03-15T00:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-03-18T00:00:00Z"
        type:
          type: string
          enum: [job_description, boolean_search, candidate_profile, meeting_notes]
          example: "job_description"
        status:
          type: string
          enum: [active, inactive]
          example: "active"
        category:
          type: string
          example: "Job Descriptions"
        versions:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              content:
                type: string
              createdAt:
                type: string
                format: date-time
          example: [
            {
              "id": "v1",
              "content": "Initial draft of Python Developer job description",
              "createdAt": "2023-03-15T00:00:00Z"
            }
          ]

  parameters:
    PaginationPage:
      name: page
      in: query
      description: Page number (1-based)
      schema:
        type: integer
        minimum: 1
        default: 1

    PaginationPageSize:
      name: pageSize
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20

    DateRangeStart:
      name: startDate
      in: query
      description: Start date for filtering (inclusive)
      schema:
        type: string
        format: date

    DateRangeEnd:
      name: endDate
      in: query
      description: End date for filtering (inclusive)
      schema:
        type: string
        format: date

    SearchQuery:
      name: q
      in: query
      description: Search query string
      schema:
        type: string

paths:
  /programs:
    get:
      summary: Get all programs
      parameters:
      - name: persona
        in: query
        schema:
          $ref: '#/components/schemas/WorkspacePersona'
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: List of programs
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Program'
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden - User doesn't have required permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create a new program
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Program'
      responses:
        '201':
          description: Program created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Program'
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          description: Conflict - Program with same title already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /programs/{programId}:
    parameters:
    - name: programId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    get:
      summary: Get a specific program
      responses:
        '200':
          description: Program details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Program'
        '404':
          description: Program not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      summary: Update a program
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Program'
      responses:
        '200':
          description: Program updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Program'
        '404':
          description: Program not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      summary: Delete a program
      responses:
        '204':
          description: Program deleted successfully
        '404':
          description: Program not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: Cannot delete program with active objectives
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /objectives:
    get:
      summary: Get objectives
      parameters:
      - name: programId
        in: query
        schema:
          type: string
          format: uuid
      - name: persona
        in: query
        schema:
          $ref: '#/components/schemas/WorkspacePersona'
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: List of objectives
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Objective'

    post:
      summary: Create a new objective
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Objective'
      responses:
        '201':
          description: Objective created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Objective'

  /objectives/{objectiveId}:
    parameters:
    - name: objectiveId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    get:
      summary: Get a specific objective
      responses:
        '200':
          description: Objective details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Objective'

    put:
      summary: Update an objective
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Objective'
      responses:
        '200':
          description: Objective updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Objective'

    delete:
      summary: Delete an objective
      responses:
        '204':
          description: Objective deleted successfully

  /tasks:
    get:
      summary: Get tasks
      parameters:
      - name: objectiveId
        in: query
        schema:
          type: string
          format: uuid
        example: "obj-1"
      - name: persona
        in: query
        schema:
          $ref: '#/components/schemas/WorkspacePersona'
        example: "recruiter"
      - name: type
        in: query
        schema:
          type: string
          enum: [ meeting, posting, sourcing, screening, job_description, other ]
        example: "posting"
      - name: status
        in: query
        schema:
          type: string
          enum: [ pending, in_progress, completed ]
      - name: priority
        in: query
        schema:
          type: string
          enum: [ low, medium, high ]
      - $ref: '#/components/parameters/DateRangeStart'
      - $ref: '#/components/parameters/DateRangeEnd'
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: List of tasks
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Task'
              example:
                data: [
                  {
                    "id": "task-1",
                    "objectiveId": "obj-1",
                    "title": "Post Senior Python Developer job on Indeed",
                    "description": "Format the job description and post it on Indeed portal",
                    "dueDate": "2024-04-05",
                    "status": "pending",
                    "priority": "high",
                    "assignedTo": "user-1",
                    "relatedItems": ["job-1"],
                    "persona": "recruiter",
                    "type": "posting",
                    "noteId": "posting-note-1"
                  }
                ],
                pagination: {
                  "total": 28,
                  "page": 1,
                  "pageSize": 20,
                  "totalPages": 2,
                  "hasNext": true,
                  "hasPrevious": false
                }

    post:
      summary: Create a new task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Task'
      responses:
        '201':
          description: Task created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'

  /tasks/{taskId}:
    parameters:
    - name: taskId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    get:
      summary: Get a specific task
      responses:
        '200':
          description: Task details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'

    put:
      summary: Update a task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Task'
      responses:
        '200':
          description: Task updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'

    delete:
      summary: Delete a task
      responses:
        '204':
          description: Task deleted successfully

  /clients:
    get:
      summary: Get all clients
      parameters:
      - $ref: '#/components/parameters/SearchQuery'
      - name: status
        in: query
        schema:
          type: string
          enum: [ active, inactive, lead, prospect ]
      - name: industry
        in: query
        schema:
          type: string
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: List of clients
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Client'

    post:
      summary: Create a new client
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Client'
      responses:
        '201':
          description: Client created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Client'

  /clients/{clientId}:
    parameters:
    - name: clientId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    get:
      summary: Get a specific client
      responses:
        '200':
          description: Client details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Client'

    put:
      summary: Update a client
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Client'
      responses:
        '200':
          description: Client updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Client'

    delete:
      summary: Delete a client
      responses:
        '204':
          description: Client deleted successfully

  /jobs:
    get:
      summary: Get all jobs
      parameters:
      - $ref: '#/components/parameters/SearchQuery'
      - name: department
        in: query
        schema:
          type: string
      - name: location
        in: query
        schema:
          type: string
      - name: status
        in: query
        schema:
          type: string
          enum: [ active, inactive, draft, archived ]
      - name: type
        in: query
        schema:
          type: string
          enum: [ job_description, boolean_search, candidate_profile, meeting_notes ]
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: List of jobs
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Job'

    post:
      summary: Create a new job
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Job'
      responses:
        '201':
          description: Job created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'

  /jobs/{jobId}:
    parameters:
    - name: jobId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    get:
      summary: Get a specific job
      responses:
        '200':
          description: Job details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'

    put:
      summary: Update a job
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Job'
      responses:
        '200':
          description: Job updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'

    delete:
      summary: Delete a job
      responses:
        '204':
          description: Job deleted successfully

  /tasks/{taskId}/notes:
    parameters:
    - name: taskId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    get:
      summary: Get notes for a task
      responses:
        '200':
          description: Notes for the task
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Note'

    post:
      summary: Create a note for a task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Note'
      responses:
        '201':
          description: Note created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Note'

  /tasks/{taskId}/notes/{noteId}:
    parameters:
    - name: taskId
      in: path
      required: true
      schema:
        type: string
        format: uuid
    - name: noteId
      in: path
      required: true
      schema:
        type: string
        format: uuid

    put:
      summary: Update a note
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Note'
      responses:
        '200':
          description: Note updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Note'

    delete:
      summary: Delete a note
      responses:
        '204':
          description: Note deleted successfully

  /notes/search:
    get:
      summary: Search across all notes
      parameters:
      - $ref: '#/components/parameters/SearchQuery'
      - name: type
        in: query
        schema:
          type: string
          enum: [ meeting, posting, sourcing, screening ]
      - $ref: '#/components/parameters/DateRangeStart'
      - $ref: '#/components/parameters/DateRangeEnd'
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: Search results
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        $ref: '#/components/schemas/Note'

  /notes/bulk:
    post:
      summary: Bulk create notes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/Note'
      responses:
        '201':
          description: Notes created
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Note'

  /clients/{clientId}/activity:
    get:
      summary: Get client activity history
      parameters:
      - name: clientId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - $ref: '#/components/parameters/DateRangeStart'
      - $ref: '#/components/parameters/DateRangeEnd'
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: Client activity log
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          type:
                            type: string
                            enum: [ job_posted, note_added, status_changed, contact_updated ]
                          timestamp:
                            type: string
                            format: date-time
                          details:
                            type: object

  /jobs/{jobId}/applications:
    get:
      summary: Get job applications
      parameters:
      - name: jobId
        in: path
        required: true
        schema:
          type: string
          format: uuid
      - name: status
        in: query
        schema:
          type: string
          enum: [ new, reviewed, shortlisted, rejected ]
      - $ref: '#/components/parameters/PaginationPage'
      - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: Job applications
          content:
            application/json:
              schema:
                allOf:
                - $ref: '#/components/schemas/PaginatedResponse'
                - type: object
                  properties:
                    data:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          candidateName:
                            type: string
                          status:
                            type: string
                            enum: [ new, reviewed, shortlisted, rejected ]
                          appliedAt:
                            type: string
                            format: date-time
                          resumeUrl:
                            type: string
                          notes:
                            type: string

  /workspace/items:
    get:
      summary: Get workspace items
      parameters:
        - name: persona
          in: query
          schema:
            $ref: '#/components/schemas/WorkspacePersona'
        - name: type
          in: query
          schema:
            type: string
            enum: [job_description, boolean_search, candidate_profile, meeting_notes]
        - $ref: '#/components/parameters/SearchQuery'
        - $ref: '#/components/parameters/PaginationPage'
        - $ref: '#/components/parameters/PaginationPageSize'
      responses:
        '200':
          description: List of workspace items
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/WorkspaceItem'
              example:
                data:
                  - id: "1"
                    title: "Senior Python Developer - Fintech"
                    content: "We are seeking a highly skilled and motivated Python Developer..."
                    tags: ["Python", "Fintech", "Senior"]
                    createdAt: "2023-03-15T00:00:00Z"
                    updatedAt: "2023-03-18T00:00:00Z"
                    type: "job_description"
                    status: "active"
                    category: "Job Descriptions"
                    versions:
                      - id: "v1"
                        content: "Initial draft of Python Developer job description"
                        createdAt: "2023-03-15T00:00:00Z"
                pagination:
                  total: 4
                  page: 1
                  pageSize: 20
                  totalPages: 1
                  hasNext: false
                  hasPrevious: false
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /workspace/blocks:
    get:
      summary: Get initial blocks for a workspace item type
      parameters:
        - name: type
          in: query
          required: true
          schema:
            type: string
            enum: [job_description, boolean_search, candidate_profile]
      responses:
        '200':
          description: List of initial blocks
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    type:
                      type: string
                      enum: [heading, subheading, text, job_details, bullet, boolean_search, candidate_rating]
                    content:
                      type: string
              example:
                - id: "block-1"
                  type: "heading"
                  content: "Senior Python Developer - Fintech"
                - id: "block-2"
                  type: "subheading"
                  content: "Overview"
                - id: "block-3"
                  type: "text"
                  content: "We are seeking a highly skilled and motivated Senior Python Developer to join our dynamic Fintech team..."
                - id: "block-4"
                  type: "job_details"
                  content: "{\"company\":\"Acme Financial\",\"location\":\"San Francisco, CA (Hybrid)\",\"salary\":\"$150,000 - $180,000\",\"experience\":\"5+ years\"}"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /workspace/personas:
    get:
      summary: Get available workspace personas
      responses:
        '200':
          description: List of workspace personas
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WorkspacePersona'
              example: ["recruiter", "hr-generalist", "benefits", "learning"]
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

security:
- BearerAuth: []
