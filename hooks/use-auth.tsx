"use client"

export function useAuth() {
  // Custom function to check if a user exists before attempting login
  const checkUserExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/check-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      const data = await response.json() as { exists: boolean };
      return data.exists;
    } catch (error) {
      console.error("Error checking user existence:", error);
      return false;
    }
  };
  return { checkUserExists };
}
