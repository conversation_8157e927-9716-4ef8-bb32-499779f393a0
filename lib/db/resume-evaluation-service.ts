// Database service for resume evaluations operations
import type { SupabaseClient } from '@supabase/supabase-js';

export interface ResumeEvaluation {
  id: string;
  task_id: string;
  candidate_name: string;
  resume_text: string;
  job_description: string;
  similarity_score: number;
  evaluation_result: {
    candidate_name: string;
    overall_score: number;
    skills_match: {
      score: number;
      present_skills: string[];
      missing_skills: string[];
      explanation: string;
    };
    experience_relevance: {
      score: number;
      explanation: string;
    };
    recommendations: string[];
  };
  jury_flag: boolean | null;
  jury_critic: string | null;
  created_at: string;
  updated_at: string;
}

export async function createResumeEvaluation(
  supabase: SupabaseClient,
  evaluationData: Omit<ResumeEvaluation, 'id' | 'created_at' | 'updated_at'>
): Promise<ResumeEvaluation> {
  const { data, error } = await supabase
    .from('resume_evaluations')
    .insert([evaluationData])
    .select()
    .single();

  if (error) {
    console.error('Error creating resume evaluation:', error);
    throw new Error(`Failed to create resume evaluation: ${error.message}`);
  }

  return data as ResumeEvaluation;
}

export async function getResumeEvaluationsByTaskId(
  supabase: SupabaseClient,
  taskId: string
): Promise<ResumeEvaluation[]> {
  const { data, error } = await supabase
    .from('resume_evaluations')
    .select('*')
    .eq('task_id', taskId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching resume evaluations:', error);
    throw new Error(`Failed to fetch resume evaluations: ${error.message}`);
  }

  return data as ResumeEvaluation[];
}

export async function updateResumeEvaluation(
  supabase: SupabaseClient,
  evaluationId: string,
  updates: Partial<Pick<ResumeEvaluation, 'jury_flag' | 'jury_critic'>>
): Promise<ResumeEvaluation> {
  const { data, error } = await supabase
    .from('resume_evaluations')
    .update(updates)
    .eq('id', evaluationId)
    .select()
    .single();

  if (error) {
    console.error('Error updating resume evaluation:', error);
    throw new Error(`Failed to update resume evaluation: ${error.message}`);
  }

  return data as ResumeEvaluation;
}

export async function deleteResumeEvaluation(
  supabase: SupabaseClient,
  evaluationId: string
): Promise<void> {
  const { error } = await supabase
    .from('resume_evaluations')
    .delete()
    .eq('id', evaluationId);

  if (error) {
    console.error('Error deleting resume evaluation:', error);
    throw new Error(`Failed to delete resume evaluation: ${error.message}`);
  }
}
