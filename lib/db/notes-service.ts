// Database service for notes operations
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Note, MeetingNote, PostingNote, SourcingNote, ScreeningNote } from '@/components/workspace/types';

// Helper function to convert database row to Note type
function transformDatabaseNote(dbNote: any): Note {
  // Convert snake_case fields to camelCase and handle date conversions
  const note: any = {
    id: dbNote.id,
    taskId: dbNote.task_id || dbNote.taskId,
    type: dbNote.type,
    lastEdited: new Date(dbNote.last_edited || dbNote.lastEdited)
  };

  // Handle date conversion for meeting notes
  if (dbNote.type === 'meeting' && dbNote.date) {
    note.date = new Date(dbNote.date);
  }

  // Copy other fields, handling both database format and API format
  const fieldsToSkip = ['id', 'task_id', 'taskId', 'type', 'last_edited', 'lastEdited', 'date'];
  
  Object.keys(dbNote).forEach(key => {
    if (!fieldsToSkip.includes(key)) {
      note[key] = dbNote[key];
    }
  });

  // Handle content field if it exists (JSONB data)
  if (dbNote.content && typeof dbNote.content === 'object') {
    Object.assign(note, dbNote.content);
    
    // Convert dates in content if needed
    if (dbNote.type === 'meeting' && dbNote.content.date) {
      note.date = new Date(dbNote.content.date);
    }
  }

  return note as Note;
}

export async function createNoteInDatabase(supabase: SupabaseClient, noteData: Omit<Note, 'id' | 'lastEdited'>): Promise<Note> {

  // Extract base fields vs content fields
  const { taskId, type, ...contentFields } = noteData;

  const dbRecord = {
    task_id: taskId,
    type: type,
    title: (contentFields as any).title,
    content: contentFields,
    last_edited: new Date()
  };

  const { data, error } = await supabase
    .from('notes')
    .insert([dbRecord])
    .select()
    .single();

  if (error) {
    console.error('Error creating note in database:', error);
    throw new Error(`Failed to create note: ${error.message}`);
  }

  return transformDatabaseNote(data);
}

export async function getNoteByTaskId(supabase: SupabaseClient, taskId: string): Promise<Note | null> {

  const { data, error } = await supabase
    .from('notes')
    .select('*')
    .eq('task_id', taskId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned - this is normal for tasks without notes
      return null;
    }
    console.error('Error fetching note:', error);
    throw new Error(`Failed to fetch note: ${error.message}`);
  }

  return transformDatabaseNote(data);
}

export async function updateNoteInDatabase(supabase: SupabaseClient, noteId: string, updates: Partial<Note>): Promise<Note> {

  // Extract base fields vs content fields
  const { id, taskId, type, lastEdited, ...contentFields } = updates;

  const dbUpdates: any = {
    last_edited: new Date()
  };

  // Update title if provided
  if ((contentFields as any).title) {
    dbUpdates.title = (contentFields as any).title;
  }

  // Update content fields
  if (Object.keys(contentFields).length > 0) {
    dbUpdates.content = contentFields;
  }

  const { data, error } = await supabase
    .from('notes')
    .update(dbUpdates)
    .eq('id', noteId)
    .select()
    .single();

  if (error) {
    console.error('Error updating note in database:', error);
    throw new Error(`Failed to update note: ${error.message}`);
  }

  return transformDatabaseNote(data);
}
