// Data service abstraction layer to handle DEMO mode and Supabase integration
// This provides a consistent interface for data fetching regardless of the data source

import { 
  getMockPrograms, 
  getClientMockPrograms 
} from '@/components/workspace/mock-data/programs';
import { 
  getMockObjectives, 
  getClientMockObjectives 
} from '@/components/workspace/mock-data/objectives';
import { 
  getMockTasks, 
  getClientMockTasks 
} from '@/components/workspace/mock-data/tasks';
import { mockClients } from '@/components/workspace/mock-data/clients';
import { mockJobs } from '@/components/workspace/mock-data/jobs';
import { getNoteByTaskId } from '@/components/workspace/mock-data/notes';

import type { WorkspacePersona, Program, Objective, TaskType, Client, Job, Note } from '@/components/workspace/types';

// Helper to determine if we're in DEMO mode
const isDemoMode = () => {
  const demoMode = process.env.NEXT_PUBLIC_DEMO_MODE;
  return demoMode === 'true' || demoMode === 'TRUE';
};

// Helper to get the correct base URL for API calls
const getBaseUrl = () => {
  // In browser, use current origin
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // In server context, try to get from environment or use localhost
  if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    return `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`;
  }
  
  // For Cloudflare Workers or local dev
  return 'http://localhost:8787';
};

// API error type
type APIError = {
  message: string;
  code: string;
  status: number;
};

// Generic API response wrapper
type APIResponse<T> = {
  data: T | null;
  error?: APIError;
};

// Fetch wrapper with error handling and retries
async function fetchWithRetry<T>(
  url: string, 
  options: RequestInit = {}, 
  retries = 3
): Promise<APIResponse<T>> {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error: APIError = {
        message: `HTTP error! status: ${response.status}`,
        code: 'API_ERROR',
        status: response.status,
      };
      throw error;
    }

    const data = await response.json();
    return { data: data as T };
  } catch (err) {
    // Type guard for Error objects
    const error = err instanceof Error ? err : new Error('Unknown error occurred');
    
    if (retries > 0) {
      // Wait 1 second before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
      return fetchWithRetry<T>(url, options, retries - 1);
    }
    
    return {
      data: null,
      error: {
        message: error.message,
        code: 'FETCH_ERROR',
        status: 500,
      },
    };
  }
}

// Core data service functions
export const getPrograms = async (persona?: WorkspacePersona) => {
  if (isDemoMode()) {
    if (persona === undefined) {
      return getMockPrograms();
    }
    return getMockPrograms(persona);
  }
  
  // Real API call to internal route handler (which uses Supabase)
  let apiUrl = '/api/programs';
  const params = new URLSearchParams();
  if (persona) {
    params.append('persona', persona.toString());
  }
  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }
  
  const response = await fetchWithRetry(apiUrl);
  if (response.error || !response.data) {
    console.error('Failed to fetch programs:', response.error);
    throw new Error(`Failed to fetch programs: ${response.error?.message || 'No data received'}`);
  }
  
  return response.data;
};

export const createProgram = async (program: Omit<Program, 'id' | 'createdAt' | 'updatedAt'>): Promise<Program> => {
  if (isDemoMode()) {
    // In demo mode, just return the program with a mock ID
    return {
      ...program,
      id: `mock-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  const response = await fetchWithRetry('/api/programs', {
    method: 'POST',
    body: JSON.stringify(program)
  });
  
  if (response.error || !response.data) {
    console.error('Failed to create program:', response.error);
    throw new Error(`Failed to create program: ${response.error?.message || 'No data received'}`);
  }
  
  return response.data as Program;
};

export const getObjectives = async (programId?: string, persona?: WorkspacePersona) => {
  if (isDemoMode()) {
    if (persona === undefined) {
      return getMockObjectives(programId);
    }
    return getMockObjectives(programId, persona);
  }
  
  // Real API call to internal route handler (which uses Supabase)
  let apiUrl = '/api/objectives';
  const params = new URLSearchParams();
  if (programId) {
    params.append('programId', programId);
  }
  if (persona) {
    params.append('persona', persona.toString());
  }
  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }
  
  const response = await fetchWithRetry(apiUrl);
  if (response.error) {
    console.error('Failed to fetch objectives:', response.error);
    throw new Error(`Failed to fetch objectives: ${response.error.message}`);
  }
  
  return response.data;
};

export const createObjective = async (objective: Omit<Objective, 'id' | 'createdAt' | 'updatedAt'>): Promise<Objective> => {
  if (isDemoMode()) {
    // In demo mode, just return the objective with a mock ID
    return {
      ...objective,
      id: `mock-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    } as Objective;
  }

  const response = await fetchWithRetry('/api/objectives', {
    method: 'POST',
    body: JSON.stringify(objective)
  });
  
  if (response.error || !response.data) {
    console.error('Failed to create objective:', response.error);
    throw new Error(`Failed to create objective: ${response.error?.message || 'No data received'}`);
  }
  
  return response.data as Objective;
};

export const getTasks = async (objectiveId?: string, persona?: WorkspacePersona) => {
  if (isDemoMode()) {
    if (persona === undefined) {
      return getMockTasks(objectiveId);
    }
    return getMockTasks(objectiveId, persona);
  }
  
  // Real API call to internal route handler (which uses Supabase)
  let apiUrl = '/api/tasks';
  const params = new URLSearchParams();
  if (objectiveId) {
    params.append('objectiveId', objectiveId);
  }
  if (persona) {
    params.append('persona', persona.toString());
  }
  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }
  
  const response = await fetchWithRetry(apiUrl);
  if (response.error) {
    console.error('Failed to fetch tasks:', response.error);
    throw new Error(`Failed to fetch tasks: ${response.error.message}`);
  }
  
  return response.data;
};

export const createTask = async (task: Omit<TaskType, 'id'>): Promise<TaskType> => {
  if (isDemoMode()) {
    // In demo mode, just return the task with a mock ID
    return {
      ...task,
      id: `mock-${Date.now()}`,
    } as TaskType;
  }

  const response = await fetchWithRetry('/api/tasks', {
    method: 'POST',
    body: JSON.stringify(task)
  });
  
  if (response.error || !response.data) {
    console.error('Failed to create task:', response.error);
    throw new Error(`Failed to create task: ${response.error?.message || 'No data received'}`);
  }
  
  return response.data as TaskType;
};

export const getTaskById = async (taskId: string, objectiveId: string, persona?: WorkspacePersona) => {
  // This function relies on getTasks. If getTasks is updated to fetch from an API,
  // this function might need to change to call a specific endpoint like /api/tasks/:id
  // or continue to filter as it does, assuming getTasks fetches all relevant tasks.
  // For now, its internal logic remains the same, but it benefits from getTasks changes.
  if (isDemoMode()) {
    // Assuming mock data structure allows this direct filtering
    const tasks = await getTasks(objectiveId, persona);
    if (Array.isArray(tasks)) {
      return tasks.find((t: any) => t.id === taskId) || null;
    }
    return null;
  }

  // Real API call to a specific task endpoint via internal route handler
  // This assumes an endpoint like /api/tasks/:taskId?objectiveId=...&persona=...
  // Adjust the endpoint as per your actual route handler structure.
  let apiUrl = `/api/tasks/${taskId}`;
  const params = new URLSearchParams();
  if (objectiveId) { // objectiveId might be part of the route or a query param
    params.append('objectiveId', objectiveId);
  }
  if (persona) {
    params.append('persona', persona.toString());
  }
  if (params.toString()) {
    apiUrl += `?${params.toString()}`;
  }

  const response = await fetchWithRetry(apiUrl);
  if (response.error) {
    console.error(`Failed to fetch task ${taskId}:`, response.error);
    // It's often better to return null or a specific error structure than to throw here
    // depending on how the caller handles errors.
    // For consistency with previous error handling:
    throw new Error(`Failed to fetch task ${taskId}: ${response.error.message}`);
  }
  return response.data;
};

export const getClients = async () => {
  if (isDemoMode()) {
    return mockClients;
  }
  
  // Real API call - direct to our Supabase-backed endpoint
  const response = await fetch('/api/clients');
  if (!response.ok) {
    const errorData = await response.json() as { error?: string };
    console.error('Failed to fetch clients:', errorData);
    throw new Error(`Failed to fetch clients: ${errorData.error || 'Unknown error'}`);
  }
  
  const clients = await response.json() as any[];
  
  // Ensure dates are properly parsed
  return clients.map((client: any) => ({
    ...client,
    createdAt: new Date(client.createdAt),
    updatedAt: new Date(client.updatedAt)
  }));
};

export const createClient = async (client: Omit<Client, 'id' | 'createdAt' | 'updatedAt' | 'orgId' | 'openJobs' | 'totalPlacements'>): Promise<Client> => {
  if (isDemoMode()) {
    // In demo mode, just return the client with mock values
    return {
      ...client,
      id: `mock-${Date.now()}`,
      orgId: 'mock-org',
      openJobs: 0,
      totalPlacements: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    } as Client;
  }

  const response = await fetch('/api/clients', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(client)
  });
  
  if (!response.ok) {
    const errorData = await response.json() as { error?: string };
    console.error('Failed to create client:', errorData);
    throw new Error(`Failed to create client: ${errorData.error || 'Unknown error'}`);
  }
  
  const newClient = await response.json() as any;
  
  // Ensure dates are properly parsed
  return {
    ...newClient,
    createdAt: new Date(newClient.createdAt),
    updatedAt: new Date(newClient.updatedAt)
  };
};

export const getJobs = async () => {
  if (isDemoMode()) {
    return mockJobs;
  }
  
  // Real API call - direct to our Supabase-backed endpoint
  const response = await fetch('/api/jobs');
  if (!response.ok) {
    const errorData = await response.json() as { error?: string };
    console.error('Failed to fetch jobs:', errorData);
    throw new Error(`Failed to fetch jobs: ${errorData.error || 'Unknown error'}`);
  }
  
  const jobs = await response.json() as any[];
  
  // Ensure dates are properly parsed
  return jobs.map((job: any) => ({
    ...job,
    createdAt: new Date(job.createdAt),
    updatedAt: new Date(job.updatedAt)
  }));
};

export const createJob = async (job: Omit<Job, 'id' | 'createdAt' | 'updatedAt' | 'versions'>): Promise<Job> => {
  if (isDemoMode()) {
    // In demo mode, just return the job with mock values
    return {
      ...job,
      id: `mock-${Date.now()}`,
      versions: [],
      createdAt: new Date(),
      updatedAt: new Date()
    } as Job;
  }

  const response = await fetch('/api/jobs', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(job)
  });
  
  if (!response.ok) {
    const errorData = await response.json() as { error?: string };
    console.error('Failed to create job:', errorData);
    throw new Error(`Failed to create job: ${errorData.error || 'Unknown error'}`);
  }
  
  const newJob = await response.json() as any;
  
  // Ensure dates are properly parsed
  return {
    ...newJob,
    createdAt: new Date(newJob.createdAt),
    updatedAt: new Date(newJob.updatedAt)
  };
};

export const getNotes = async (taskId: string) => {
  if (isDemoMode()) {
    return getNoteByTaskId(taskId);
  }
  
  // Real API call to internal route handler (which uses Supabase)
  const response = await fetchWithRetry(`/api/notes?taskId=${taskId}`);
  if (response.error) {
    console.error('Failed to fetch notes:', response.error);
    // Return null instead of throwing error for missing notes
    return null;
  }
  
  // Handle different response formats
  if (response.data) {
    // Check if response is an empty array - return null for no notes
    if (Array.isArray(response.data) && response.data.length === 0) {
      return null;
    }
    
    // Process single note object with date conversion
    const noteData = response.data as any;
    const convertedNote: any = { ...noteData };
    
    // Convert lastEdited to Date if it's a string
    if (convertedNote.lastEdited && typeof convertedNote.lastEdited === 'string') {
      convertedNote.lastEdited = new Date(convertedNote.lastEdited);
    }
    
    // Convert date field for meeting notes
    if (convertedNote.type === 'meeting' && convertedNote.date && typeof convertedNote.date === 'string') {
      convertedNote.date = new Date(convertedNote.date);
    }
    
    return convertedNote as Note;
  }
  
  return null;
};

export const createNote = async (note: Omit<Note, 'id' | 'lastEdited'>): Promise<Note> => {
  if (isDemoMode()) {
    // In demo mode, just return the note with mock values
    return {
      ...note,
      id: `mock-${Date.now()}`,
      lastEdited: new Date()
    } as Note;
  }

  const response = await fetchWithRetry('/api/notes', {
    method: 'POST',
    body: JSON.stringify(note)
  });
  
  if (response.error || !response.data) {
    console.error('Failed to create note:', response.error);
    throw new Error(`Failed to create note: ${response.error?.message || 'No data received'}`);
  }
  
  // Convert date fields in the response
  const noteData = response.data as any;
  const convertedNote: any = { ...noteData };
  
  // Convert lastEdited to Date if it's a string
  if (convertedNote.lastEdited && typeof convertedNote.lastEdited === 'string') {
    convertedNote.lastEdited = new Date(convertedNote.lastEdited);
  }
  
  // Convert date field for meeting notes
  if (convertedNote.type === 'meeting' && convertedNote.date && typeof convertedNote.date === 'string') {
    convertedNote.date = new Date(convertedNote.date);
  }
  
  return convertedNote as Note;
};

export const updateNote = async (noteId: string, updates: Partial<Note>): Promise<Note> => {
  if (isDemoMode()) {
    // In demo mode, just return the updated note
    return {
      ...updates,
      id: noteId,
      lastEdited: new Date()
    } as Note;
  }

  const response = await fetchWithRetry(`/api/notes/${noteId}`, {
    method: 'PUT',
    body: JSON.stringify(updates)
  });
  
  if (response.error || !response.data) {
    console.error('Failed to update note:', response.error);
    throw new Error(`Failed to update note: ${response.error?.message || 'No data received'}`);
  }
  
  return response.data as Note;
};

export const updateClient = async (clientId: string, updates: Partial<Client>): Promise<Client> => {
  if (isDemoMode()) {
    // In demo mode, just return the updated client with mock values
    return {
      ...updates,
      id: clientId,
      orgId: 'mock-org',
      updatedAt: new Date()
    } as Client;
  }

  const response = await fetch(`/api/clients/${clientId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(updates)
  });
  
  if (!response.ok) {
    const errorData = await response.json() as { error?: string };
    console.error('Failed to update client:', errorData);
    throw new Error(`Failed to update client: ${errorData.error || 'Unknown error'}`);
  }
  
  const updatedClient = await response.json() as any;
  
  // Ensure dates are properly parsed
  return {
    ...updatedClient,
    createdAt: new Date(updatedClient.createdAt),
    updatedAt: new Date(updatedClient.updatedAt)
  };
};