import { redirect } from "next/navigation";
import { auth } from "@/app/auth";
import { ProfileClient } from "@/components/profile/profile-client";

export default async function ProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Server-side auth check
  const session = await auth();
  if (!session) {
    redirect("/login");
  }
  
  return (
    <ProfileClient>
      {children}
    </ProfileClient>
  );
}
