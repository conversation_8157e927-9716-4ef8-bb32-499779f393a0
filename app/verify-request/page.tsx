"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { MailChe<PERSON>, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useSearchParams } from "next/navigation"

export default function VerifyRequestPage() {
  const searchParams = useSearchParams();
  const email = searchParams.get("email") || "";
  
  return (
    <div className="min-h-screen bg-cream-100 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            <div className="text-forest-600 font-bold text-xl">O6</div>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card className="border-gray-200/50 shadow-sm">
            <CardHeader className="text-center">
              <div className="mx-auto bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                <MailCheck className="h-8 w-8 text-green-600" />
              </div>              <CardTitle className="text-2xl">Check your email</CardTitle>
              <CardDescription>
                {email ? (
                  <>We've sent an authentication link to <span className="font-medium">{email}</span></>
                ) : (
                  <>We've sent you an authentication link</>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-center text-sm text-gray-600">
                Click the link in your email to authenticate and sign in securely.
                The link will expire after 15 minutes.
              </p>
              
              <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
                <p className="text-sm text-blue-700">
                  <strong>Authentication in progress!</strong> Once you click the link in your email,
                  you'll be automatically signed in to your account.
                </p>
              </div>
              
              <div className="flex flex-col space-y-2">
                <Link href="/login">
                  <Button 
                    variant="outline"
                    className="w-full border-gray-200 text-gray-700"
                  >
                    Back to login
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
