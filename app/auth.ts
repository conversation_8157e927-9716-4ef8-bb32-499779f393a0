import NextAuth from "next-auth";
import { NextAuthResult } from "next-auth";
import { SupabaseAdapter } from "@auth/supabase-adapter";
import Resend from "next-auth/providers/resend";
import { getCloudflareContext } from "@opennextjs/cloudflare";

const authResult = async (): Promise<NextAuthResult> => {
  // Check if we're in development mode or production
  let env: any;

  if (process.env.NODE_ENV === 'development') {
    // Use process.env for local development
    env = process.env;
  } else {
    // Use Cloudflare context for production
    const context = await getCloudflareContext({async: true});
    env = context.env;
  }

  console.log('SUPABASE_URL:', env.SUPABASE_URL);

  return NextAuth({
    providers: [
      Resend({
        apiKey: env.AUTH_RESEND_KEY,
        from: env.AUTH_EMAIL_FROM,
      }),
    ],
    adapter: SupabaseAdapter({
      url: env.SUPABASE_URL,
      secret: env.SUPABASE_SERVICE_ROLE_KEY,
    }),
    callbacks: {
      async session({ session, token, user }) {
        // Add user data to the session
        if (session.user) {
          session.user.id = user.id;
        }
        return session;
      }
    },
    pages: {
      signIn: "/login",
      verifyRequest: "/verify-request",
      error: "/auth/error",
      newUser:"/register",
    }
  });
};

export const { handlers, signIn, signOut, auth } = await authResult();