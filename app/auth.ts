import NextAuth from "next-auth";
import { NextAuthResult } from "next-auth";
import { SupabaseAdapter } from "@auth/supabase-adapter";
import Resend from "next-auth/providers/resend";
import { getCloudflareContext } from "@opennextjs/cloudflare";

const authResult = async (): Promise<NextAuthResult> => {
  const context = await getCloudflareContext({async: true});
  console.log(context.env.SUPABASE_URL);
    return NextAuth({
    providers: [
      Resend({
        apiKey: context.env.AUTH_RESEND_KEY,
        from: context.env.AUTH_EMAIL_FROM,
      }),
    ],    adapter: SupabaseAdapter({
      url: context.env.SUPABASE_URL,
      secret: context.env.SUPABASE_SERVICE_ROLE_KEY,
    }),
    callbacks: {
      async session({ session, token, user }) {
        // Add user data to the session
        if (session.user) {
          session.user.id = user.id;
        }
        return session;
      }
    },
    pages: {
      signIn: "/login",
      verifyRequest: "/verify-request",
      error: "/auth/error",
      newUser:"/register",
    }
  });
};

export const { handlers, signIn, signOut, auth } = await authResult();