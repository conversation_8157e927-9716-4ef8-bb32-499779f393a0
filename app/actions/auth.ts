'use server'

import { signIn } from '../auth'

export async function signInWithResend({ email, callbackUrl }: { email: string, callbackUrl: string }) {
  // Check if we're in demo mode
  const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true';

  if (isDemoMode) {
    // In demo mode, just return success without actually sending email
    console.log('Demo mode: Simulating sign-in for:', email);
    return {
      success: true,
      message: "Demo sign-in successful",
      demo: true
    };
  }

  return await signIn("resend", {
    email,
    redirect: false,
    redirectTo: callbackUrl,
  });
}
