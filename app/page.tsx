import { redirect } from 'next/navigation';
import { signIn, signOut, auth } from './auth';
import { updateRecord } from '@auth/d1-adapter';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';

async function updateName(formData: FormData): Promise<void> {
  'use server';
  const session = await auth();
  if (!session?.user?.id) {
    return;
  }
  const name = formData.get('name') as string;
  if (!name) {
    return;
  }
  const query = `UPDATE users SET name = $1 WHERE id = $2`;
  await updateRecord((await getCloudflareContext({async: true})).env.DB, query, [name, session.user.id]);
  redirect('/');
}

export default async function Home() {
  const session = await auth();
  
  // If user is authenticated, redirect to dashboard
  if (session) {
    redirect('/home/<USER>');
  }
    return (
    <main className="min-h-screen bg-cream-100 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="text-forest-600 font-bold text-xl">O6</div>
            <div>
              <Button variant="ghost" asChild>
                <a href="/login">Log in</a>
              </Button>
              <Button className="ml-2 bg-forest-600 hover:bg-forest-700 text-white" asChild>
                <a href="/register">Sign up</a>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero section */}
      <div className="flex-1 flex items-center justify-center p-4 bg-cream-50">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Modern business platform for the AI era
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            O6 brings together your team, tools, and data in one simple platform
            built to make your organization more productive and efficient.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button className="bg-forest-600 hover:bg-forest-700 text-white px-6 py-2 h-12 text-lg" asChild>
              <a href="/register">Get started for free</a>
            </Button>
            <Button variant="outline" className="px-6 py-2 h-12 text-lg" asChild>
              <a href="/login">Log in to your account</a>
            </Button>
          </div>
        </div>
      </div>
    </main>
  );
}