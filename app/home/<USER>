import type React from "react"
import { redirect } from "next/navigation"
import { homeNavigation } from "@/components/workspace/navigation/home-navigation"
import { WorkspaceModule } from "@/components/workspace/workspace-module" 
import { auth } from "@/app/auth"

export default async function HomeLayout({ children }: { children: React.ReactNode }) {
  // Check authentication server-side for stronger protection
  const session = await auth()
  
  // If no session exists, redirect to login
  if (!session) {
    redirect('/login')
  }
  
  return (
    <div className="h-screen flex flex-col">
      <WorkspaceModule 
        title="O6" 
        navigation={homeNavigation} 
        module="home"
        user={session.user}
      >
        {children}
      </WorkspaceModule>
    </div>
  )
}

