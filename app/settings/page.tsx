import { WorkspaceModule } from "@/components/workspace/workspace-module"
import { redirect } from 'next/navigation';
import { auth } from "@/app/auth";

export default async function SettingsPage() {
  const session = await auth();
  
  // If user is not authenticated, redirect to home page
  if (!session) {
    redirect('/');
  }
  
  return (
    <div className="h-screen">
      <WorkspaceModule title="O6" module="settings" user={session.user} />
    </div>
  )
}

