import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle, Building, Users, Shield } from "lucide-react"
import { redirect } from 'next/navigation';
import { auth } from "@/app/auth";

export default async function LandingPage() {
  const session = await auth();
  
  // If user is authenticated, redirect to dashboard
  if (session) {
    redirect('/home/<USER>');
  }
  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      <header className="border-b border-gray-200/50 bg-white">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <div className="text-forest-600 font-bold text-2xl">Catalyst</div>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/login" className="text-gray-600 hover:text-forest-600">
              Login
            </Link>
            <Link href="/register">
              <Button className="bg-forest-600 hover:bg-forest-700 text-white">Get Started</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-cream-100 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              The Modern HR & Recruiting Platform for Growing Businesses
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Streamline your HR operations, simplify recruiting, and manage your workforce all in one place.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/register">
                <Button size="lg" className="bg-forest-600 hover:bg-forest-700 text-white">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="#features">
                <Button size="lg" variant="outline" className="border-forest-600 text-forest-600 hover:bg-cream-200">
                  See Features
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Everything You Need to Manage Your Workforce</h2>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-cream-50 p-6 rounded-lg border border-gray-200/50 shadow-sm">
              <div className="bg-forest-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-forest-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Human Capital Management</h3>
              <p className="text-gray-600 mb-4">
                Manage recruiting, HR operations, benefits, and learning & development in one unified platform.
              </p>
              <ul className="space-y-2">
                {["Recruiting", "HR Management", "Benefits Administration", "Learning & Development"].map((item) => (
                  <li key={item} className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-forest-600 mr-2 flex-shrink-0 mt-0.5" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-cream-50 p-6 rounded-lg border border-gray-200/50 shadow-sm">
              <div className="bg-forest-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Building className="h-6 w-6 text-forest-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Client Relationship Management</h3>
              <p className="text-gray-600 mb-4">
                Manage client relationships, track job openings, and streamline your business development process.
              </p>
              <ul className="space-y-2">
                {["Client Management", "Job Tracking", "Candidate Matching", "Placement Analytics"].map((item) => (
                  <li key={item} className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-forest-600 mr-2 flex-shrink-0 mt-0.5" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-cream-50 p-6 rounded-lg border border-gray-200/50 shadow-sm">
              <div className="bg-forest-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-forest-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Organization Management</h3>
              <p className="text-gray-600 mb-4">
                Secure, role-based access control for your entire organization with comprehensive audit trails.
              </p>
              <ul className="space-y-2">
                {["User Management", "Role-Based Access", "Audit Logs", "Security Controls"].map((item) => (
                  <li key={item} className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-forest-600 mr-2 flex-shrink-0 mt-0.5" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-forest-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Transform Your HR Operations?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of businesses that use Catalyst to streamline their HR and recruiting processes.
          </p>
          <Link href="/register">
            <Button size="lg" className="bg-white text-forest-600 hover:bg-cream-100">
              Start Your Free Trial
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-100 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-8 md:mb-0">
              <div className="text-forest-600 font-bold text-xl mb-4">Catalyst</div>
              <p className="text-gray-600 max-w-xs">The modern HR & recruiting platform for growing businesses.</p>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
              <div>
                <h3 className="font-semibold mb-4">Product</h3>
                <ul className="space-y-2">
                  {["Features", "Pricing", "Integrations", "Roadmap"].map((item) => (
                    <li key={item}>
                      <a href="#" className="text-gray-600 hover:text-forest-600">
                        {item}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-4">Company</h3>
                <ul className="space-y-2">
                  {["About", "Customers", "Careers", "Contact"].map((item) => (
                    <li key={item}>
                      <a href="#" className="text-gray-600 hover:text-forest-600">
                        {item}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-4">Resources</h3>
                <ul className="space-y-2">
                  {["Blog", "Documentation", "Support", "Legal"].map((item) => (
                    <li key={item}>
                      <a href="#" className="text-gray-600 hover:text-forest-600">
                        {item}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-200 mt-12 pt-8 text-center text-gray-600">
            <p>&copy; {new Date().getFullYear()} Catalyst. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

