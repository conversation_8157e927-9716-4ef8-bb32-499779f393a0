// app/api/notes/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';
import { createNoteInDatabase, getNoteByTaskId } from '@/lib/db/notes-service';
import type { Note } from '@/components/workspace/types';

export async function GET(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return NextResponse.json(
        { error: 'taskId is required' },
        { status: 400 }
      );
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    const note = await getNoteByTaskId(supabase, taskId);
    // Return empty array if no notes found instead of null/error
    return NextResponse.json(note || []);
  } catch (error) {
    console.error('Failed to fetch notes:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    // Return empty array on error to avoid frontend issues
    return NextResponse.json([], { status: 200 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json() as any; // Type assertion for now - will be validated below
    
    // Validate required fields based on note type
    if (!body.taskId || !body.type) {
      return NextResponse.json(
        { error: 'taskId and type are required' },
        { status: 400 }
      );
    }

    // Validate note type is one of the supported types
    const validTypes = ['meeting', 'posting', 'sourcing', 'screening'];
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        { error: `Invalid note type. Must be one of: ${validTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Set lastEdited timestamp if not provided
    if (!body.lastEdited) {
      body.lastEdited = new Date();
    }

    const note = await createNoteInDatabase(supabase, body as Omit<Note, 'id' | 'lastEdited'>);
    return NextResponse.json(note, { status: 201 });
  } catch (error) {
    console.error('Failed to create note:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ message: 'Failed to create note', error: errorMessage }, { status: 500 });
  }
}
