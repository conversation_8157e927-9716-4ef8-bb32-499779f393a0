// app/api/objectives/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const programId = searchParams.get('programId');
    const persona = searchParams.get('persona');

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Build query - join with programs to ensure objectives belong to user's org
    let query = supabase
      .from('objectives')
      .select(`
        *,
        programs!inner(org_id)
      `)
      .eq('programs.org_id', userData.org_id)
      .order('created_at', { ascending: false });

    // Filter by programId if provided
    if (programId) {
      query = query.eq('program_id', programId);
    }

    // Filter by persona if provided
    if (persona) {
      query = query.eq('persona', persona);
    }

    const { data: objectives, error } = await query;

    if (error) {
      console.error('Error fetching objectives:', error);
      return NextResponse.json({ error: "Failed to fetch objectives" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedObjectives = objectives.map(objective => ({
      id: objective.id,
      programId: objective.program_id,
      title: objective.title,
      description: objective.description,
      month: objective.month,
      year: objective.year,
      status: objective.status,
      progress: objective.progress,
      owner: objective.owner,
      persona: objective.persona,
      startDate: objective.start_date ? new Date(objective.start_date) : new Date(),
      createdAt: new Date(objective.created_at),
      updatedAt: new Date(objective.updated_at)
    }));

    return NextResponse.json(transformedObjectives);
  } catch (error) {
    console.error('Failed to fetch objectives:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { programId, title, description, month, year, status, progress, owner, persona, startDate } = body;

    // Validate required fields
    if (!programId || !title || !owner || !persona || month === undefined || !year) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization and verify program ownership
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify program belongs to user's organization
    const { data: programData, error: programError } = await supabase
      .from('programs')
      .select('id')
      .eq('id', programId)
      .eq('org_id', userData.org_id)
      .single();

    if (programError || !programData) {
      return NextResponse.json({ error: "Program not found or access denied" }, { status: 404 });
    }

    // Create objective
    const { data: objective, error } = await supabase
      .from('objectives')
      .insert([{
        program_id: programId,
        title,
        description,
        month,
        year,
        status: status || 'not_started',
        progress: progress || 0,
        owner,
        persona,
        start_date: startDate ? new Date(startDate).toISOString().split('T')[0] : null
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating objective:', error);
      return NextResponse.json({ error: "Failed to create objective" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedObjective = {
      id: objective.id,
      programId: objective.program_id,
      title: objective.title,
      description: objective.description,
      month: objective.month,
      year: objective.year,
      status: objective.status,
      progress: objective.progress,
      owner: objective.owner,
      persona: objective.persona,
      startDate: objective.start_date ? new Date(objective.start_date) : new Date(),
      createdAt: new Date(objective.created_at),
      updatedAt: new Date(objective.updated_at)
    };

    return NextResponse.json(transformedObjective, { status: 201 });
  } catch (error) {
    console.error('Failed to create objective:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
