// app/api/tasks/[taskId]/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export async function GET(
  request: Request,
  { params: routeParams }: { params: Promise<{ taskId: string }> }
) {
  const session = await auth();
  const { searchParams } = new URL(request.url);
  const objectiveId = searchParams.get('objectiveId'); // Optional, as per your data-service
  const persona = searchParams.get('persona'); // Optional
  const { taskId } = await routeParams;

  let externalApiUrl = `${API_BASE_URL}/tasks/${taskId}`;
  const queryParams = new URLSearchParams();
  if (objectiveId) {
    queryParams.append('objectiveId', objectiveId);
  }
  if (persona) {
    queryParams.append('persona', persona);
  }
  if (queryParams.toString()) {
    externalApiUrl += `?${queryParams.toString()}`;
  }

  try {
    const response = await fetch(externalApiUrl, {
      headers: {
        "Authorization": `Bearer ${session?.sessionToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error(`External API Error fetching task ${taskId}:`, errorData);
      return NextResponse.json({ message: `Error fetching task ${taskId}: ${response.statusText}`, details: errorData }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Failed to fetch task ${taskId} from external API:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ message: `Failed to fetch task ${taskId}`, error: errorMessage }, { status: 500 });
  }
}

export async function PATCH(
  request: Request,
  { params: routeParams }: { params: Promise<{ taskId: string }> }
) {
  const session = await auth();
  
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { taskId } = await routeParams;
  const updates = await request.json();

  if (!updates) {
    return NextResponse.json(
      { error: 'No update data provided' },
      { status: 400 }
    );
  }

  if (process.env.NEXT_PUBLIC_DEMO_MODE === 'TRUE' || process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
    // In demo mode, return a successful mock response
    return NextResponse.json({ ...updates, id: taskId });
  }

  try {
    const externalApiUrl = `${API_BASE_URL}/tasks/${taskId}`;
    
    const response = await fetch(externalApiUrl, {
      method: 'PATCH',
      headers: {
        "Authorization": `Bearer ${session?.sessionToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updates)
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error(`External API Error updating task ${taskId}:`, errorData);
      return NextResponse.json({ message: `Error updating task ${taskId}: ${response.statusText}`, details: errorData }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Failed to update task ${taskId}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ message: `Failed to update task ${taskId}`, error: errorMessage }, { status: 500 });
  }
}