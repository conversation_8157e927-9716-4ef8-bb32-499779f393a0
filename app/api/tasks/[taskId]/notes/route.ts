// app/api/tasks/[taskId]/notes/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export async function GET(
  request: Request,
  { params: routeParams }: { params: Promise<{ taskId: string }> }
) {
  const session = await auth();
  const { taskId } = await routeParams;

  const externalApiUrl = `${API_BASE_URL}/tasks/${taskId}/notes`;

  try {
    const response = await fetch(externalApiUrl, {
      headers: {
        "Authorization": `Bearer ${session?.sessionToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error(`External API Error fetching notes for task ${taskId}:`, errorData);
      return NextResponse.json({ message: `Error fetching notes for task ${taskId}: ${response.statusText}`, details: errorData }, { status: response.status });
    }

    const data = await response.json();
    // Consistent with data-service.ts: process dates and return first note or null
    if (data && Array.isArray(data)) {
      const processedData = data.map(note => {
        if (note.lastEdited && typeof note.lastEdited === 'string') {
          return { ...note, lastEdited: new Date(note.lastEdited) };
        }
        return note;
      });
      return NextResponse.json(processedData[0] || null);
    } else if (data && data.lastEdited && typeof data.lastEdited === 'string') {
        // Handle if the API returns a single note object directly
        return NextResponse.json({ ...data, lastEdited: new Date(data.lastEdited) });
    }
    
    return NextResponse.json(data); // Fallback for other data structures

  } catch (error) {
    console.error(`Failed to fetch notes for task ${taskId} from external API:`, error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    // Return null instead of error for missing notes to avoid frontend issues
    return NextResponse.json(null, { status: 200 });
  }
}