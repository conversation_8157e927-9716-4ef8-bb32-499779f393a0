// app/api/programs/route.ts
import { auth } from "@/app/auth";
import { NextResponse } from 'next/server';
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const persona = searchParams.get('persona');

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Build query
    let query = supabase
      .from('programs')
      .select('*')
      .eq('org_id', userData.org_id)
      .order('created_at', { ascending: false });

    // Filter by persona if provided
    if (persona) {
      query = query.eq('persona', persona);
    }

    const { data: programs, error } = await query;

    if (error) {
      console.error('Error fetching programs:', error);
      return NextResponse.json({ error: "Failed to fetch programs" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedPrograms = programs.map(program => ({
      id: program.id,
      title: program.title,
      description: program.description,
      quarter: program.quarter,
      year: program.year,
      status: program.status,
      progress: program.progress,
      owner: program.owner,
      persona: program.persona,
      createdAt: new Date(program.created_at),
      updatedAt: new Date(program.updated_at)
    }));

    return NextResponse.json(transformedPrograms);
  } catch (error) {
    console.error('Failed to fetch programs:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, quarter, year, status, progress, owner, persona } = body;

    // Validate required fields
    if (!title || !quarter || !year || !owner || !persona) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Get Supabase access
    const context = await getCloudflareContext({ async: true });
    if (!context.env.SUPABASE_URL || !context.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase URL or Service Role Key is not configured.");
    }
    
    const supabase = createClient(context.env.SUPABASE_URL, context.env.SUPABASE_SERVICE_ROLE_KEY);

    // Get user's organization
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('org_id')
      .eq('id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Create program
    const { data: program, error } = await supabase
      .from('programs')
      .insert([{
        org_id: userData.org_id,
        title,
        description,
        quarter,
        year,
        status: status || 'planning',
        progress: progress || 0,
        owner,
        persona
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating program:', error);
      return NextResponse.json({ error: "Failed to create program" }, { status: 500 });
    }

    // Transform database data to match frontend types
    const transformedProgram = {
      id: program.id,
      title: program.title,
      description: program.description,
      quarter: program.quarter,
      year: program.year,
      status: program.status,
      progress: program.progress,
      owner: program.owner,
      persona: program.persona,
      createdAt: new Date(program.created_at),
      updatedAt: new Date(program.updated_at)
    };

    return NextResponse.json(transformedProgram, { status: 201 });
  } catch (error) {
    console.error('Failed to create program:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}