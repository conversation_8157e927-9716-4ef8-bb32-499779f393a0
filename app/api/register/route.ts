import { NextResponse } from "next/server";
import { getCloudflareContext } from "@opennextjs/cloudflare";
import { SupabaseAdapter } from "@auth/supabase-adapter";
import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@supabase/supabase-js';

// Define an interface for the expected request body
interface RegisterRequestBody {
  firstName: string;
  lastName: string;
  email: string;
  organizationName: string;
  industry: string; // Added industry
  size: string;     // Added size
}

export async function POST(request: Request) {
  try {
    const body = await request.json() as RegisterRequestBody; // Type assertion
    // Destructure industry and size
    const { firstName, lastName, email, organizationName, industry, size } = body;

    // Input validation - include industry and size
    if (!firstName || !lastName || !email || !organizationName || !industry || !size) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Production mode - create real user and organization

    // Get environment variables - handle both development and production
    let env: any;

    if (process.env.NODE_ENV === 'development') {
      // Use process.env for local development
      env = process.env;
    } else {
      // Use Cloudflare context for production
      const context = await getCloudflareContext({ async: true });
      env = context.env;
    }

    const adapter = SupabaseAdapter({
      url: env.SUPABASE_URL,
      secret: env.SUPABASE_SERVICE_ROLE_KEY,
    });

    // Check if getUserByEmail method exists and then if user already exists
    if (typeof adapter.getUserByEmail !== 'function') {
      throw new Error("getUserByEmail method not available on adapter");
    }
    const existingUser = await adapter.getUserByEmail(email);

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }
    
    const userId = uuidv4();
    
    if (typeof adapter.createUser !== 'function') {
      throw new Error("createUser method not available on adapter");
    }
    
    try {
      const authUser = await adapter.createUser({
        id: userId,
        name: `${firstName} ${lastName}`,
        email: email,
        emailVerified: null, // Remains null until email verification
        image: null 
      });
      
      if (!authUser) {
        throw new Error("Failed to create user record in auth schema");
      }

      // Initialize Supabase client for public schema operations
      // Ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are defined in env
      if (!env.SUPABASE_URL || !env.SUPABASE_SERVICE_ROLE_KEY) {
        throw new Error("Supabase URL or Service Role Key is not configured.");
      }
      const supabase = createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY);

      // 1. Create Organization
      const organizationId = uuidv4();
      const { data: orgData, error: orgError } = await supabase
        .from('organizations') // Ensure this table name matches your public schema
        .insert([{ 
            id: organizationId, 
            name: organizationName,
            industry: industry, // Store industry
            size: size          // Store size
            // Add other organization fields here if they are part of `body` and your table
        }])
        .select()
        .single();

      if (orgError || !orgData) {
        console.error("Error creating organization:", orgError);
        // Consider rolling back authUser creation or logging for manual intervention
        throw new Error(`Failed to create organization: ${orgError?.message || 'No data returned'}`);
      }

      // 2. Get default Persona ID (e.g., "Owner")
      const { data: personaData, error: personaError } = await supabase
        .from('personas') // Ensure this table name matches your public schema
        .select('id')
        .eq('name', 'Owner') // Assuming 'Owner' is the name of the default persona for the creator
        .single();

      if (personaError || !personaData) {
        console.error("Error fetching default Owner persona:", personaError);
        // Note: authUser and organization were created. Consider cleanup logic for production.
        throw new Error(`Failed to fetch default Owner persona: ${personaError?.message || 'Default Owner persona not found'}`);
      }
      const defaultPersonaId = personaData.id;

      // 3. Create User in public.users table
      // Ensure this table name and column names match your public schema
      const { data: publicUserData, error: publicUserError } = await supabase
        .from('users') 
        .insert([{
            id: authUser.id, // Use the ID from the auth.users record (which is `userId`)
            org_id: organizationId, 
            first_name: firstName,  
            last_name: lastName,    
            email: email,
            persona_id: defaultPersonaId // Assign the fetched Owner persona ID
        }])
        .select()
        .single();

      if (publicUserError || !publicUserData) {
        console.error("Error creating public user record:", publicUserError);
        // Consider rolling back authUser and organization creation or logging
        throw new Error(`Failed to create public user record: ${publicUserError?.message || 'No data returned'}`);
      }
      
    } catch (createError: any) {
      console.error("Error during user and organization creation process:", createError);
      throw new Error(`Failed during user and organization setup: ${createError.message}`);
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: error.message || "Failed to process registration" },
      { status: 500 }
    );
  }
}
