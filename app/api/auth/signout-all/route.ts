import { NextResponse } from "next/server";
import { auth } from "@/app/auth";
import { getCloudflareContext } from "@opennextjs/cloudflare";

export async function POST(request: Request) {
  try {
    // Get the current session
    const session = await auth();
    
    if (!session || !session.user?.email) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Need to implement later
    
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Sign out all error:", error);
    return NextResponse.json(
      { error: "Failed to sign out from all sessions" },
      { status: 500 }
    );
  }
}
