"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, MailCheck } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { signInWithResend } from "../../actions/auth"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function LoginPage() {
  const router = useRouter()
  const { checkUserExists } = useAuth()
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [emailSent, setEmailSent] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    
    try {
      // First check if the user exists in the database
      const userExists = await checkUserExists(email)
      
      if (!userExists) {
        setError("No account found with this email. Please sign up instead.")
        setIsLoading(false)
        return
      }
      
      // User exists, send magic link using server action
      const result = await signInWithResend({
        email,
        callbackUrl: "/home/<USER>"
      })
      if (result?.error) {
        setError("Failed to send magic link. Please try again.")
      } else {
        setEmailSent(true)
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.")
      console.error("Sign in error:", err)
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div className="min-h-screen bg-cream-100 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-200/50 bg-white">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            <div className="text-forest-600 font-bold text-xl">O6</div>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {!emailSent ? (
            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader>
                <CardTitle className="text-2xl">Log in to your account</CardTitle>
                <CardDescription>Enter your email to receive a magic link for secure access.</CardDescription>
              </CardHeader>
              <form onSubmit={handleSubmit}>
                <CardContent className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="email">Email address</Label>
                    <Input 
                      id="email" 
                      name="email" 
                      type="email" 
                      value={email} 
                      onChange={(e) => setEmail(e.target.value)} 
                      required 
                      disabled={isLoading}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    type="submit" 
                    className="w-full bg-forest-600 hover:bg-forest-700 text-white"
                    disabled={isLoading}
                  >
                    {isLoading ? "Sending..." : "Send Verification Link"}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          ) : (            <Card className="border-gray-200/50 shadow-sm">
              <CardHeader className="text-center">
                <div className="mx-auto bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-4">
                  <MailCheck className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-2xl">Check your email</CardTitle>
                <CardDescription>
                  We've sent an authentication link to <span className="font-medium">{email}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-center text-sm text-gray-600">
                  Click the link in your email to authenticate and log in securely.
                  The authentication link will expire after 15 minutes.
                </p>
                <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
                  <p className="text-sm text-blue-700">
                    <strong>Authentication successful!</strong> Once you click the link in your email,
                    you'll be automatically signed in to your account.
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2">
                <Button 
                  type="button" 
                  className="w-full bg-forest-600 hover:bg-forest-700 text-white"
                  onClick={() => setEmailSent(false)}
                >
                  Use a different email
                </Button>                <Button 
                  type="button" 
                  variant="outline"
                  className="w-full border-gray-200 text-gray-700"
                  onClick={async () => {
                    setIsLoading(true);
                    try {
                      await signInWithResend({
                        email,
                        callbackUrl: "/home/<USER>"
                      });
                    } catch (err) {
                      console.error("Resend error:", err);
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? "Sending..." : "Resend authentication link"}
                </Button>
              </CardFooter>
            </Card>
          )}

          <p className="text-center mt-4 text-sm text-gray-600">
            Don't have an account?{" "}
            <Link href="/register" className="text-forest-600 hover:underline">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

