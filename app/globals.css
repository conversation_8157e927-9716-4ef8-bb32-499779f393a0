@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 60 20% 95%;
    --foreground: 150 30% 10%;

    --card: 0 0% 100%;
    --card-foreground: 150 30% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 150 30% 10%;

    --primary: 150 30% 23%;
    --primary-foreground: 0 0% 98%;

    --secondary: 60 20% 96%;
    --secondary-foreground: 150 30% 10%;

    --muted: 60 20% 96%;
    --muted-foreground: 150 30% 40%;

    --accent: 150 30% 96%;
    --accent-foreground: 150 30% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 150 30% 90%;
    --input: 150 30% 90%;
    --ring: 150 30% 23%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 150 30% 10%;
    --foreground: 0 0% 98%;

    --card: 150 30% 10%;
    --card-foreground: 0 0% 98%;

    --popover: 150 30% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 150 30% 23%;
    --primary-foreground: 0 0% 98%;

    --secondary: 150 30% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 150 30% 15.9%;
    --muted-foreground: 150 30% 64.9%;

    --accent: 150 30% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 150 30% 15.9%;
    --input: 150 30% 15.9%;
    --ring: 150 30% 23%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-cream-200 text-foreground;
  }
}

@layer components {
  .forest-card {
    @apply bg-white border border-gray-200/50 shadow-sm rounded-lg;
  }

  .forest-button {
    @apply bg-forest-600 text-white font-medium shadow-sm hover:bg-forest-700 transition-all;
  }

  .forest-button-secondary {
    @apply bg-cream-300 text-forest-600 font-medium shadow-sm hover:bg-cream-400 transition-all;
  }

  .forest-sidebar {
    @apply bg-forest-600 text-white;
  }

  .forest-content {
    @apply bg-white;
  }

  .forest-nav-item {
    @apply flex items-center gap-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg px-3 py-2 transition-all;
  }

  .forest-nav-item.active {
    @apply text-white bg-white/10 font-medium;
  }

  .forest-tab {
    @apply px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-forest-600 transition-colors;
  }

  .forest-tab.active {
    @apply bg-white text-forest-600 shadow-sm;
  }

  .forest-priority-high {
    @apply bg-red-100 text-red-800 border-l-4 border-red-500;
  }

  .forest-priority-medium {
    @apply bg-amber-100 text-amber-800 border-l-4 border-amber-500;
  }

  .forest-priority-low {
    @apply bg-green-100 text-green-800 border-l-4 border-green-500;
  }
}

