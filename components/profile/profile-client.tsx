"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { signOut } from "next-auth/react";
import { toast } from "sonner";

interface ProfileClientProps {
  children: React.ReactNode;
}

export function ProfileClient({ children }: ProfileClientProps) {
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOutAllSessions = async () => {
    if (confirm("Are you sure you want to sign out of all sessions? You'll need to sign in again on all devices.")) {
      setIsSigningOut(true);
      try {
        // Call an API endpoint that will invalidate all sessions for this user
        await fetch("/api/auth/signout-all", { method: "POST" });
        
        // Then sign out the current session
        await signOut({ redirectTo: "/login" });
        
        toast.success("Signed out of all sessions");
      } catch (error) {
        console.error("Error signing out of all sessions:", error);
        toast.error("Failed to sign out of all sessions");
        setIsSigningOut(false);
      }
    }
  };

  return (
    <div onClick={(e) => {
      const target = e.target as HTMLElement;
      if (target.tagName === "BUTTON" && target.innerText === "Sign out of all sessions") {
        e.preventDefault();
        handleSignOutAllSessions();
      }
    }}>
      {children}
    </div>
  );
}
