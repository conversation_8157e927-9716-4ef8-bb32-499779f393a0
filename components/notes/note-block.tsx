"use client"

import { useState } from "react"
import { Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import type { NoteBlockType } from "./notes-module"

interface NoteBlockProps {
  block: NoteBlockType
  onUpdate: (content: string) => void
  onDelete: () => void
}

export function NoteBlock({ block, onUpdate, onDelete }: NoteBlockProps) {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <div
      className="group relative border border-transparent hover:border-border rounded-md p-2"
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
    >
      {block.type === "heading" && (
        <Input
          value={block.content}
          onChange={(e) => onUpdate(e.target.value)}
          className="text-2xl font-bold border-none focus-visible:ring-0 p-0"
          placeholder="Heading"
        />
      )}

      {block.type === "subheading" && (
        <Input
          value={block.content}
          onChange={(e) => onUpdate(e.target.value)}
          className="text-xl font-semibold border-none focus-visible:ring-0 p-0"
          placeholder="Subheading"
        />
      )}

      {block.type === "text" && (
        <Textarea
          value={block.content}
          onChange={(e) => onUpdate(e.target.value)}
          className="min-h-[100px] border-none focus-visible:ring-0 p-0 resize-none"
          placeholder="Start typing..."
        />
      )}

      {block.type === "bullet" && (
        <div className="flex items-start">
          <span className="mr-2 mt-1.5">•</span>
          <Textarea
            value={block.content}
            onChange={(e) => onUpdate(e.target.value)}
            className="min-h-[60px] border-none focus-visible:ring-0 p-0 resize-none"
            placeholder="Bullet point"
          />
        </div>
      )}

      {block.type === "numbered" && (
        <div className="flex items-start">
          <span className="mr-2 font-medium">1.</span>
          <Textarea
            value={block.content}
            onChange={(e) => onUpdate(e.target.value)}
            className="min-h-[60px] border-none focus-visible:ring-0 p-0 resize-none"
            placeholder="Numbered item"
          />
        </div>
      )}

      {block.type === "code" && (
        <Textarea
          value={block.content}
          onChange={(e) => onUpdate(e.target.value)}
          className="min-h-[120px] font-mono text-sm border-none focus-visible:ring-0 p-2 bg-muted rounded-md"
          placeholder="// Code block"
        />
      )}

      {(isFocused || block.content === "") && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute -right-2 -top-2 opacity-0 group-hover:opacity-100 focus:opacity-100"
          onClick={onDelete}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}

