"use client"

import type React from "react"

import { useState } from "react"
import { ChevronDown, ChevronRight, FileText, Folder, Plus, Search, Tag } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import type { Note } from "./notes-module"

interface NotesSidebarProps {
  notes: Note[]
  folders: string[]
  tags: string[]
  activeNote: Note | null
  onNoteSelect: (note: Note) => void
  onCreateNewNote: () => void
  searchQuery: string
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  activeTab: string
  setActiveTab: (tab: string) => void
}

export function NotesSidebar({
  notes,
  folders,
  tags,
  activeNote,
  onNoteSelect,
  onCreateNewNote,
  searchQuery,
  onSearchChange,
  activeTab,
  setActiveTab,
}: NotesSidebarProps) {
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>(
    folders.reduce((acc, folder) => ({ ...acc, [folder]: true }), {}),
  )

  const toggleFolder = (folder: string) => {
    setExpandedFolders({
      ...expandedFolders,
      [folder]: !expandedFolders[folder],
    })
  }

  const folderNotes = folders.reduce<Record<string, Note[]>>((acc, folder) => {
    acc[folder] = notes.filter((note) => note.folder === folder)
    return acc
  }, {})

  const unorganizedNotes = notes.filter((note) => !folders.includes(note.folder))

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 flex justify-between items-center">
        <h2 className="font-semibold text-lg">Notes</h2>
        <Button size="sm" onClick={onCreateNewNote}>
          <Plus className="h-4 w-4 mr-1" />
          New
        </Button>
      </div>

      <div className="px-4 pb-2">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search notes..." className="pl-8" value={searchQuery} onChange={onSearchChange} />
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="mx-4 mb-2">
          <TabsTrigger value="all" className="flex-1">
            All
          </TabsTrigger>
          <TabsTrigger value="folders" className="flex-1">
            Folders
          </TabsTrigger>
          <TabsTrigger value="tags" className="flex-1">
            Tags
          </TabsTrigger>
        </TabsList>

        <ScrollArea className="flex-1">
          <TabsContent value="all" className="m-0 p-0">
            <div className="px-2">
              {notes.map((note) => (
                <Button
                  key={note.id}
                  variant="ghost"
                  className={cn("w-full justify-start mb-1 font-normal", activeNote?.id === note.id && "bg-accent")}
                  onClick={() => onNoteSelect(note)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  <span className="truncate">{note.title}</span>
                </Button>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="folders" className="m-0 p-0">
            <div className="px-2">
              {folders.map((folder) => (
                <div key={folder}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start mb-1 font-medium"
                    onClick={() => toggleFolder(folder)}
                  >
                    {expandedFolders[folder] ? (
                      <ChevronDown className="h-4 w-4 mr-2" />
                    ) : (
                      <ChevronRight className="h-4 w-4 mr-2" />
                    )}
                    <Folder className="h-4 w-4 mr-2" />
                    <span>{folder}</span>
                    <span className="ml-auto text-xs text-muted-foreground">{folderNotes[folder]?.length || 0}</span>
                  </Button>

                  {expandedFolders[folder] &&
                    folderNotes[folder]?.map((note) => (
                      <Button
                        key={note.id}
                        variant="ghost"
                        className={cn(
                          "w-full justify-start mb-1 font-normal pl-9",
                          activeNote?.id === note.id && "bg-accent",
                        )}
                        onClick={() => onNoteSelect(note)}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        <span className="truncate">{note.title}</span>
                      </Button>
                    ))}
                </div>
              ))}

              {unorganizedNotes.length > 0 && (
                <div>
                  <Button
                    variant="ghost"
                    className="w-full justify-start mb-1 font-medium"
                    onClick={() => toggleFolder("Unorganized")}
                  >
                    {expandedFolders["Unorganized"] ? (
                      <ChevronDown className="h-4 w-4 mr-2" />
                    ) : (
                      <ChevronRight className="h-4 w-4 mr-2" />
                    )}
                    <Folder className="h-4 w-4 mr-2" />
                    <span>Unorganized</span>
                    <span className="ml-auto text-xs text-muted-foreground">{unorganizedNotes.length}</span>
                  </Button>

                  {expandedFolders["Unorganized"] &&
                    unorganizedNotes.map((note) => (
                      <Button
                        key={note.id}
                        variant="ghost"
                        className={cn(
                          "w-full justify-start mb-1 font-normal pl-9",
                          activeNote?.id === note.id && "bg-accent",
                        )}
                        onClick={() => onNoteSelect(note)}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        <span className="truncate">{note.title}</span>
                      </Button>
                    ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="tags" className="m-0 p-0">
            <div className="px-2">
              {tags.map((tag) => (
                <div key={tag}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start mb-1 font-medium"
                    onClick={() => toggleFolder(tag)}
                  >
                    {expandedFolders[tag] ? (
                      <ChevronDown className="h-4 w-4 mr-2" />
                    ) : (
                      <ChevronRight className="h-4 w-4 mr-2" />
                    )}
                    <Tag className="h-4 w-4 mr-2" />
                    <span>{tag}</span>
                    <span className="ml-auto text-xs text-muted-foreground">
                      {notes.filter((note) => note.tags.includes(tag)).length}
                    </span>
                  </Button>

                  {expandedFolders[tag] &&
                    notes
                      .filter((note) => note.tags.includes(tag))
                      .map((note) => (
                        <Button
                          key={note.id}
                          variant="ghost"
                          className={cn(
                            "w-full justify-start mb-1 font-normal pl-9",
                            activeNote?.id === note.id && "bg-accent",
                          )}
                          onClick={() => onNoteSelect(note)}
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          <span className="truncate">{note.title}</span>
                        </Button>
                      ))}
                </div>
              ))}
            </div>
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  )
}

