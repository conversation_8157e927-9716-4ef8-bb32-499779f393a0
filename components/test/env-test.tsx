'use client';

import { useEffect, useState } from 'react';

export default function EnvTest() {
  const [demoMode, setDemoMode] = useState<string | undefined>(undefined);
  
  useEffect(() => {
    // Get environment variable in client component
    setDemoMode(process.env.NEXT_PUBLIC_DEMO_MODE);
    console.log('NEXT_PUBLIC_DEMO_MODE in client component:', process.env.NEXT_PUBLIC_DEMO_MODE);
  }, []);
  
  return (
    <div className="p-4 border rounded-md m-4 bg-slate-100">
      <h2 className="text-xl font-bold mb-2">Environment Variable Test</h2>
      <p className="font-mono">NEXT_PUBLIC_DEMO_MODE: {demoMode}</p>
      <p className="text-sm mt-2">Check the console for additional logs</p>
    </div>
  );
} 