"use client"

import type React from "react"

import { useState, useEffect } from "react"
import {
  <PERSON>rkles,
  X,
  Send,
  Bot,
  Share2,
  Mail,
  Users,
  MessageSquare,
  FileText,
  Filter,
  Brain,
  ListTodo,
  Lightbulb,
  HelpCircle,
  Wand2,
  Copy,
  Download,
  Calendar,
  BookOpen,
  FileEdit,
  Phone,
  File,
  FileCheck,
  BarChart2,
  Settings,
  Shield,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import type { WorkspaceItem, WorkspacePersona, Client, TaskType } from "../types"

interface FloatingAssistantProps {
  activeItem: WorkspaceItem | null
  activePersona?: WorkspacePersona
  activeClient?: Client | null
  activeTask?: TaskType | null
  isClientModule?: boolean
  isSettingsModule?: boolean
}

export function FloatingAssistant({ 
  activeItem, 
  activePersona = "recruiter",
  activeClient,
  activeTask,
  isClientModule = false,
  isSettingsModule = false
}: FloatingAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [position, setPosition] = useState({ right: 20, bottom: 30 })
  const [isHovered, setIsHovered] = useState(false)
  const [hasNewSuggestion, setHasNewSuggestion] = useState(false)

  // Close the assistant when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (isOpen && !target.closest('[data-assistant="true"]')) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen])

  // Prevent the assistant from going off-screen
  useEffect(() => {
    const handleResize = () => {
      setPosition({
        right: Math.min(20, window.innerWidth - 80),
        bottom: Math.min(30, window.innerHeight - 80),
      })
    }

    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // Show new suggestions when active item changes or has workflow actions
  useEffect(() => {
    if (activeItem) {
      // Always show notification for items with workflow actions
      const hasWorkflowActions =
        activeItem.type === "job_description" ||
        activeItem.type === "boolean_search" ||
        activeItem.type === "candidate_profile" ||
        activeItem.type === "meeting_notes"

      if (hasWorkflowActions) {
        setHasNewSuggestion(true)

        // Only auto-hide after 15 seconds if the assistant isn't open
        if (!isOpen) {
          const timer = setTimeout(() => {
            setHasNewSuggestion(false)
          }, 15000)

          return () => clearTimeout(timer)
        }
      }
    } else {
      setHasNewSuggestion(false)
    }
  }, [activeItem, isOpen])

  return (
    <>
      {/* Floating button */}
      <Button
        data-assistant="true"
        onClick={() => {
          setIsOpen(!isOpen)
          setHasNewSuggestion(false)
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "fixed rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50",
          hasNewSuggestion && !isOpen
            ? "animate-pulse bg-primary text-primary-foreground ring-2 ring-primary ring-offset-2"
            : "bg-primary text-primary-foreground",
          isHovered && "scale-110",
        )}
        style={{
          right: `${position.right}px`,
          bottom: `${position.bottom}px`,
        }}
      >
        <Bot className="h-6 w-6" />
        {hasNewSuggestion && !isOpen && (
          <span className="absolute top-0 right-0 h-3 w-3 rounded-full bg-destructive"></span>
        )}
      </Button>

      {/* Assistant popup */}
      {isOpen && (
        <Card
          data-assistant="true"
          className="fixed z-50 w-80 h-[450px] flex flex-col shadow-xl rounded-lg overflow-hidden animate-in slide-in-from-right-10 duration-200"
          style={{
            right: `${position.right + 60}px`,
            bottom: `${position.bottom}px`,
          }}
        >
          <div className="p-3 border-b border-border flex justify-between items-center bg-card">
            <div className="flex items-center">
              <Sparkles className="h-4 w-4 mr-2 text-primary" />
              <h3 className="font-medium">
                {isSettingsModule && "Settings Assistant"}
                {!isSettingsModule && isClientModule && "Client Assistant"}
                {!isSettingsModule && !isClientModule && activePersona === "recruiter" && "Recruiter Assistant"}
                {!isSettingsModule && !isClientModule && activePersona === "hr-generalist" && "HR Assistant"}
                {!isSettingsModule && !isClientModule && activePersona === "benefits" && "Benefits Assistant"}
                {!isSettingsModule && !isClientModule && activePersona === "learning" && "Learning Assistant"}
              </h3>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex-1 overflow-hidden flex flex-col">
            {/* Greeting message */}
            <div className="p-3 border-b border-border/50">
              <div className="flex items-start gap-2">
                <Bot className="h-5 w-5 text-primary mt-0.5" />
                <p className="text-sm">
                  {!activeItem && !activeTask && (
                    <>
                      {isSettingsModule && "Hello! Need help configuring your workspace or managing your account settings?"}
                      {!isSettingsModule && isClientModule && "Hello! How can I help you manage your client relationships today?"}
                      {!isSettingsModule && !isClientModule && activePersona === "recruiter" && "Hi there! How can I help with your recruiting tasks today?"}
                      {!isSettingsModule && !isClientModule && activePersona === "hr-generalist" && "Hello! Need assistance with HR management or employee relations?"}
                      {!isSettingsModule && !isClientModule && activePersona === "benefits" && "Welcome! How can I help with benefits administration today?"}
                      {!isSettingsModule && !isClientModule && activePersona === "learning" && "Hi there! Looking for help with training or development programs?"}
                    </>
                  )}
                  {activeTask && (
                    <>
                      {activeTask.type === "meeting" && 
                        `I see you're working on a meeting task "${activeTask.title}". Need help with scheduling or agenda planning?`}
                      {activeTask.type === "posting" && 
                        `Working on the job posting "${activeTask.title}"? I can help optimize the description or suggest posting channels.`}
                      {activeTask.type === "sourcing" && 
                        `Looking at the sourcing task "${activeTask.title}". Need assistance with search strategies or candidate outreach?`}
                      {activeTask.type === "screening" && 
                        `Reviewing the screening task "${activeTask.title}". I can help with interview questions or candidate evaluation.`}
                      {!activeTask.type && 
                        `I see you're working on the task "${activeTask.title}". How can I assist you with this?`}
                    </>
                  )}
                  {activeItem?.type === "job_description" &&
                    `I see you're working on "${activeItem.title}". How can I help with this job description?`}
                  {activeItem?.type === "boolean_search" &&
                    `Looking at your search for "${activeItem.title}". How can I help optimize it?`}
                  {activeItem?.type === "candidate_profile" &&
                    `Reviewing "${activeItem.title}". Need help with assessment or outreach?`}
                  {activeItem?.type === "meeting_notes" &&
                    `I see you're reviewing "${activeItem.title}". Need a summary or action items?`}
                </p>
              </div>
            </div>

            {/* Scrollable content area */}
            <ScrollArea className="flex-1">
              <div className="p-3 space-y-4">
                {/* Suggested section */}
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2">Suggested</h4>
                  <div className="space-y-1">
                    <SuggestedSection 
                      activeItem={activeItem} 
                      activePersona={activePersona} 
                      isClientModule={isClientModule}
                      isSettingsModule={isSettingsModule}
                      activeClient={activeClient}
                      activeTask={activeTask}
                    />
                  </div>
                </div>

                {/* Workflow actions section - only shown when there's an active item or task */}
                {(activeItem || activeTask) && (
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2">Actions</h4>
                    <div className="space-y-1">
                      <WorkflowActions activeItem={activeItem} activePersona={activePersona} activeTask={activeTask} />
                    </div>
                  </div>
                )}

                {/* Draft section */}
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2">Draft</h4>
                  <div className="space-y-1">
                    <DraftSection activePersona={activePersona} isClientModule={isClientModule} isSettingsModule={isSettingsModule} activeTask={activeTask} />
                  </div>
                </div>

                {/* More section */}
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2">More</h4>
                  <div className="space-y-1">
                    {activeTask && activeTask.type === "meeting" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Meeting best practices" />
                    )}
                    {activeTask && activeTask.type === "posting" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Job posting optimization tips" />
                    )}
                    {activeTask && activeTask.type === "sourcing" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Sourcing strategies guide" />
                    )}
                    {activeTask && activeTask.type === "screening" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Interview techniques" />
                    )}
                    {isSettingsModule && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Settings tutorials" />
                    )}
                    {!isSettingsModule && !activeTask && activePersona === "recruiter" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Get help with recruiting" />
                    )}
                    {!isSettingsModule && !activeTask && activePersona === "hr-generalist" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="HR policy assistance" />
                    )}
                    {!isSettingsModule && !activeTask && activePersona === "benefits" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Benefits FAQ" />
                    )}
                    {!isSettingsModule && !activeTask && activePersona === "learning" && (
                      <ActionButton icon={<HelpCircle className="h-4 w-4" />} label="Training best practices" />
                    )}
                    <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule a demo" />
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>

          <div className="p-3 border-t border-border bg-card">
            <div className="flex items-center space-x-2">
              <Input placeholder="Ask anything..." className="flex-1 h-8 text-sm" />
              <Button size="icon" className="h-8 w-8">
                <Send className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </Card>
      )}
    </>
  )
}

// Action button component for consistent styling
function ActionButton({
  icon,
  label,
  onClick,
  primary = false,
}: {
  icon: React.ReactNode
  label: string
  onClick?: () => void
  primary?: boolean
}) {
  return (
    <Button
      variant={primary ? "default" : "ghost"}
      size="sm"
      className="w-full justify-start text-sm h-8 px-2 font-normal"
      onClick={onClick}
    >
      <span className={`mr-2 ${primary ? "" : "text-muted-foreground"}`}>{icon}</span>
      {label}
    </Button>
  )
}

// Suggested actions based on the active item
function SuggestedSection({ 
  activeItem, 
  activePersona = "recruiter", 
  isClientModule = false,
  isSettingsModule = false,
  activeClient,
  activeTask
}: { 
  activeItem: WorkspaceItem | null
  activePersona?: WorkspacePersona
  isClientModule?: boolean
  isSettingsModule?: boolean
  activeClient?: Client | null
  activeTask?: TaskType | null
}) {
  // Show task-type specific suggestions when a task is active
  if (activeTask && activeTask.type) {
    switch (activeTask.type) {
      case "meeting":
        return (
          <>
            <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Generate meeting agenda" />
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Create action items" />
            <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule follow-up" />
          </>
        );
      case "posting":
        return (
          <>
            <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Optimize job description" />
            <ActionButton icon={<Share2 className="h-4 w-4" />} label="Suggest posting channels" />
            <ActionButton icon={<Brain className="h-4 w-4" />} label="Create matching search string" />
          </>
        );
      case "sourcing":
        return (
          <>
            <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Improve boolean search" />
            <ActionButton icon={<Users className="h-4 w-4" />} label="Find candidate communities" />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Draft outreach message" />
          </>
        );
      case "screening":
        return (
          <>
            <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Generate screening questions" />
            <ActionButton icon={<Brain className="h-4 w-4" />} label="Evaluate candidate fit" />
            <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Prepare feedback summary" />
          </>
        );
      default:
        return (
          <>
            <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Get task assistance" />
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Create next steps" />
          </>
        );
    }
  }

  if (!activeItem) {
    if (isSettingsModule) {
      return (
        <>
          <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Update profile information" />
          <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Configure notifications" />
        </>
      );
    }
    
    if (isClientModule) {
      return (
        <>
          <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Create client proposal" />
          <ActionButton icon={<ListTodo className="h-4 w-4" />} label="View client contracts" />
        </>
      );
    }
    
    if (activePersona === "recruiter") {
      return (
        <>
          <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Create a job description" />
          <ActionButton icon={<ListTodo className="h-4 w-4" />} label="View today's tasks" />
        </>
      );
    }
    
    if (activePersona === "hr-generalist") {
      return (
        <>
          <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Create employee handbook" />
          <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Review PTO requests" />
        </>
      );
    }
    
    if (activePersona === "benefits") {
      return (
        <>
          <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Generate benefits summary" />
          <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Review enrollment status" />
        </>
      );
    }
    
    if (activePersona === "learning") {
      return (
        <>
          <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Create training module" />
          <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Review course completions" />
        </>
      );
    }
    
    // Default fallback
    return (
      <>
        <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Create a document" />
        <ActionButton icon={<ListTodo className="h-4 w-4" />} label="View today's tasks" />
      </>
    );
  }

  if (activeItem.type === "job_description") {
    return (
      <>
        <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Enhance this description" />
        <ActionButton icon={<Brain className="h-4 w-4" />} label="Generate search string" />
        <ActionButton icon={<Lightbulb className="h-4 w-4" />} label="Suggest similar candidates" />
      </>
    )
  }

  if (activeItem.type === "boolean_search") {
    return (
      <>
        <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Optimize this search" />
        <ActionButton icon={<Brain className="h-4 w-4" />} label="Estimate candidate pool" />
        <ActionButton icon={<Lightbulb className="h-4 w-4" />} label="Add missing keywords" />
      </>
    )
  }

  if (activeItem.type === "candidate_profile") {
    return (
      <>
        <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Suggest screening questions" />
        <ActionButton icon={<Brain className="h-4 w-4" />} label="Match to open positions" />
        <ActionButton icon={<Lightbulb className="h-4 w-4" />} label="Draft outreach email" />
      </>
    )
  }

  if (activeItem.type === "meeting_notes") {
    return (
      <>
        <ActionButton icon={<Wand2 className="h-4 w-4" />} label="Summarize key points" />
        <ActionButton icon={<Brain className="h-4 w-4" />} label="Extract action items" />
        <ActionButton icon={<Lightbulb className="h-4 w-4" />} label="Create follow-up tasks" />
      </>
    )
  }

  // When we have an activeItem, return item-specific actions
  if (activeItem) {
    return activeItem.type === "candidate" ? (
      <>
        <ActionButton icon={<Mail className="h-4 w-4" />} label="Send interview invitation" />
        <ActionButton icon={<FileText className="h-4 w-4" />} label="View resume" />
      </>
    ) : (
      <>
        <ActionButton icon={<BookOpen className="h-4 w-4" />} label="View details" />
        <ActionButton icon={<FileEdit className="h-4 w-4" />} label="Edit item" />
      </>
    );
  }

  // For Clients module with an active client
  if (isClientModule && activeClient) {
    return (
      <>
        <ActionButton icon={<Phone className="h-4 w-4" />} label="Schedule client meeting" />
        <ActionButton icon={<File className="h-4 w-4" />} label="Generate contract" />
        <ActionButton icon={<Users className="h-4 w-4" />} label="View client team" />
      </>
    );
  }

  return null
}

// Workflow actions based on the active item
function WorkflowActions({ activeItem, activePersona = "recruiter", activeTask = null }: { 
  activeItem: WorkspaceItem | null
  activePersona?: WorkspacePersona 
  activeTask?: TaskType | null
}) {
  // Show task-specific workflow actions if task is active
  if (activeTask && activeTask.type) {
    switch (activeTask.type) {
      case "meeting":
        return (
          <>
            <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule Meeting" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Send Invitations" />
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Prepare Agenda" />
          </>
        );
      case "posting":
        return (
          <>
            <ActionButton icon={<Share2 className="h-4 w-4" />} label="Publish Job" primary />
            <ActionButton icon={<Users className="h-4 w-4" />} label="Review Applications" />
            <ActionButton icon={<Copy className="h-4 w-4" />} label="Duplicate Posting" />
          </>
        );
      case "sourcing":
        return (
          <>
            <ActionButton icon={<Users className="h-4 w-4" />} label="Find Candidates" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Contact Prospects" />
            <ActionButton icon={<Brain className="h-4 w-4" />} label="Analyze Results" />
          </>
        );
      case "screening":
        return (
          <>
            <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Conduct Interview" primary />
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Evaluate Candidate" />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Send Follow-up" />
          </>
        );
    }
  }

  // If no task is active but we have an activeItem, use the item-specific actions
  if (activeItem) {
    // The existing workflow actions are mostly suitable for recruiter persona
    // For other personas, we can add more specialized actions based on the item type
    
    if (activeItem.type === "job_description") {
      if (activePersona === "recruiter") {
        return (
          <>
            <ActionButton icon={<Share2 className="h-4 w-4" />} label="Post Job" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Send Bulk Mail" />
            <ActionButton icon={<Copy className="h-4 w-4" />} label="Duplicate" />
            <ActionButton icon={<Download className="h-4 w-4" />} label="Export" />
          </>
        )
      } else if (activePersona === "hr-generalist") {
        return (
          <>
            <ActionButton icon={<Share2 className="h-4 w-4" />} label="Publish Policy" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Send to Team" />
            <ActionButton icon={<Copy className="h-4 w-4" />} label="Duplicate" />
          </>
        )
      }
    }

    // Existing code for remaining item types
    if (activeItem.type === "boolean_search") {
      return (
        <>
          <ActionButton icon={<Users className="h-4 w-4" />} label="Search Candidates" primary />
          <ActionButton icon={<Share2 className="h-4 w-4" />} label="Export to Job Portal" />
          <ActionButton icon={<Copy className="h-4 w-4" />} label="Copy to Clipboard" />
        </>
      )
    }

    if (activeItem.type === "candidate_profile") {
      return (
        <>
          <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Schedule Interview" primary />
          <ActionButton icon={<Mail className="h-4 w-4" />} label="Contact Candidate" />
          <ActionButton icon={<Users className="h-4 w-4" />} label="Add to Talent Pool" />
        </>
      )
    }

    if (activeItem.type === "meeting_notes") {
      if (activePersona === "recruiter") {
        return (
          <>
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Create Recruiting Tasks" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Share Notes" />
            <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule Follow-up" />
          </>
        )
      } else if (activePersona === "hr-generalist") {
        return (
          <>
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Create HR Tasks" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Share Meeting Summary" />
            <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule Next Meeting" />
          </>
        )
      } else if (activePersona === "benefits") {
        return (
          <>
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Create Benefits Tasks" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Share Benefits Notes" />
            <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule Enrollment Meeting" />
          </>
        )
      } else if (activePersona === "learning") {
        return (
          <>
            <ActionButton icon={<ListTodo className="h-4 w-4" />} label="Create Training Tasks" primary />
            <ActionButton icon={<Mail className="h-4 w-4" />} label="Share Training Notes" />
            <ActionButton icon={<Calendar className="h-4 w-4" />} label="Schedule Training Session" />
          </>
        )
      }
    }
  }

  return null
}

// Draft suggestions based on the active persona
function DraftSection({ 
  activePersona = "recruiter", 
  isClientModule = false,
  isSettingsModule = false,
  activeTask = null
}: { 
  activePersona: WorkspacePersona
  isClientModule?: boolean
  isSettingsModule?: boolean
  activeTask?: TaskType | null
}) {
  // If we have an active task, provide relevant draft options based on task type
  if (activeTask && activeTask.type) {
    switch (activeTask.type) {
      case "meeting":
        return (
          <>
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Meeting agenda template" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Meeting summary" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Action items list" />
          </>
        );
      case "posting":
        return (
          <>
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Job description template" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Job requirements" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Company overview" />
          </>
        );
      case "sourcing":
        return (
          <>
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Boolean search string" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Candidate outreach email" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Sourcing strategy" />
          </>
        );
      case "screening":
        return (
          <>
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Interview questions" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Candidate evaluation" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Feedback template" />
          </>
        );
      default:
        return (
          <>
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Task notes" />
            <ActionButton icon={<FileText className="h-4 w-4" />} label="Progress update" />
          </>
        );
    }
  }

  if (isSettingsModule) {
    return (
      <>
        <ActionButton icon={<FileText className="h-4 w-4" />} label="User documentation" />
        <ActionButton icon={<Settings className="h-4 w-4" />} label="System preferences" />
        <ActionButton icon={<Shield className="h-4 w-4" />} label="Security policy" />
      </>
    );
  }
  
  if (isClientModule) {
    return (
      <>
        <ActionButton icon={<FileText className="h-4 w-4" />} label="Client proposal" />
        <ActionButton icon={<FileCheck className="h-4 w-4" />} label="Contract template" />
        <ActionButton icon={<BarChart2 className="h-4 w-4" />} label="Client report" />
      </>
    );
  }
  
  if (activePersona === "recruiter") {
    return (
      <>
        <ActionButton icon={<FileText className="h-4 w-4" />} label="Job description" />
        <ActionButton icon={<Filter className="h-4 w-4" />} label="Boolean search" />
        <ActionButton icon={<Users className="h-4 w-4" />} label="Candidate profile" />
        <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Meeting notes" />
      </>
    );
  }

  if (activePersona === "hr-generalist") {
    return (
      <>
        <ActionButton icon={<FileText className="h-4 w-4" />} label="Employee handbook" />
        <ActionButton icon={<Filter className="h-4 w-4" />} label="HR policies" />
        <ActionButton icon={<Users className="h-4 w-4" />} label="Employee records" />
        <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Performance reviews" />
      </>
    )
  }

  if (activePersona === "benefits") {
    return (
      <>
        <ActionButton icon={<FileText className="h-4 w-4" />} label="Benefits summary" />
        <ActionButton icon={<Filter className="h-4 w-4" />} label="Enrollment details" />
        <ActionButton icon={<Users className="h-4 w-4" />} label="Benefits claims" />
        <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Benefits meetings" />
      </>
    )
  }

  if (activePersona === "learning") {
    return (
      <>
        <ActionButton icon={<FileText className="h-4 w-4" />} label="Training materials" />
        <ActionButton icon={<Filter className="h-4 w-4" />} label="Course progress" />
        <ActionButton icon={<Users className="h-4 w-4" />} label="Learning outcomes" />
        <ActionButton icon={<MessageSquare className="h-4 w-4" />} label="Training feedback" />
      </>
    )
  }

  return null
}

