"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sparkles, X, Send } from "lucide-react"
import { cn } from "@/lib/utils"
import type { WorkspaceItem, TaskType } from "../types"

interface AiAssistantProps {
  isOpen: boolean
  activeItem: WorkspaceItem | null
  activeTask?: TaskType | null
  onClose: () => void
}

export function AiAssistant({ isOpen, activeItem, activeTask, onClose }: AiAssistantProps) {
  return (
    <div
      className={cn(
        "border-l border-border transition-all duration-300 bg-card",
        isOpen ? "w-80" : "w-0 overflow-hidden",
      )}
    >
      <div className="h-full flex flex-col">
        <div className="p-3 border-b border-border flex justify-between items-center">
          <div className="flex items-center">
            <Sparkles className="h-4 w-4 mr-2 text-primary" />
            <h3 className="font-medium">Recruiter Assistant</h3>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            <div className="flex justify-start">
              <div className="max-w-[80%] rounded-lg p-3 bg-muted">
                <div className="flex items-center mb-1">
                  <Sparkles className="h-3 w-3 mr-1 text-primary" />
                  <span className="text-xs font-medium">Recruiter Assistant</span>
                </div>
                <div>
                  {!activeItem &&
                    !activeTask &&
                    "Hello! I'm your recruiting assistant. I can help with job descriptions, boolean searches, and candidate screening. How can I help today?"}

                  {activeTask?.type === "meeting" &&
                    `I see you're working on meeting notes. I can help summarize key points, extract requirements, or draft follow-up tasks based on these notes.`}

                  {activeTask?.type === "posting" &&
                    `I see you're working on a job posting. I can help enhance the job description, suggest additional responsibilities, or create a boolean search string based on this JD.`}

                  {activeTask?.type === "sourcing" &&
                    `I see you're working on sourcing candidates. I can help optimize your search string, suggest additional keywords, or estimate the candidate pool size.`}

                  {activeTask?.type === "screening" &&
                    `I see you're reviewing a candidate profile. I can help assess skill match, suggest screening questions, or draft an outreach email.`}

                  {activeItem?.type === "job_description" &&
                    !activeTask &&
                    `I see you're working on the "${activeItem.title}" job description. I can help enhance the requirements, suggest skills to add, or create a boolean search string based on this JD.`}

                  {activeItem?.type === "boolean_search" &&
                    !activeTask &&
                    `Looking at your search string for "${activeItem.title}". I can help optimize this query, suggest additional keywords, or estimate the candidate pool size.`}

                  {activeItem?.type === "candidate_profile" &&
                    !activeTask &&
                    `Reviewing "${activeItem.title}". I can help assess skill match, suggest screening questions, or draft an outreach email.`}

                  {activeItem?.type === "meeting_notes" &&
                    !activeTask &&
                    `I see you're reviewing notes from "${activeItem.title}". I can summarize key points, extract action items, or help create follow-up tasks.`}
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        <div className="p-3 border-t border-border">
          <div className="mb-3">
            <p className="text-xs text-muted-foreground mb-2">Suggested actions:</p>
            <div className="flex flex-wrap gap-2">
              <SuggestedActions activeItem={activeItem} activeTask={activeTask} />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Input placeholder="Ask your assistant..." className="flex-1" />
            <Button size="icon">
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

function SuggestedActions({
  activeItem,
  activeTask,
}: { activeItem: WorkspaceItem | null; activeTask?: TaskType | null }) {
  if (activeTask) {
    switch (activeTask.type) {
      case "meeting":
        return (
          <>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Extract requirements
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Draft job description
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Create follow-up tasks
            </Button>
          </>
        )
      case "posting":
        return (
          <>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Enhance job description
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Add missing skills
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Generate boolean search
            </Button>
          </>
        )
      case "sourcing":
        return (
          <>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Optimize search string
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Add exclusion terms
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Estimate candidate pool
            </Button>
          </>
        )
      case "screening":
        return (
          <>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Assess skill match
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Suggest interview questions
            </Button>
            <Button variant="outline" size="sm" className="text-xs h-7">
              Draft outreach email
            </Button>
          </>
        )
      default:
        return null
    }
  }

  if (!activeItem) {
    return (
      <>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Create job description template
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Suggest tasks for today
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Recruitment best practices
        </Button>
      </>
    )
  }

  if (activeItem.type === "job_description") {
    return (
      <>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Enhance this job description
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Generate boolean search from JD
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Suggest similar candidates
        </Button>
      </>
    )
  }

  if (activeItem.type === "boolean_search") {
    return (
      <>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Optimize this search string
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Estimate candidate pool
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Add missing keywords
        </Button>
      </>
    )
  }

  if (activeItem.type === "candidate_profile") {
    return (
      <>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Suggest screening questions
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Match to open positions
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Draft outreach email
        </Button>
      </>
    )
  }

  if (activeItem.type === "meeting_notes") {
    return (
      <>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Summarize key points
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Extract action items
        </Button>
        <Button variant="outline" size="sm" className="text-xs h-7">
          Create follow-up tasks
        </Button>
      </>
    )
  }

  return null
}

