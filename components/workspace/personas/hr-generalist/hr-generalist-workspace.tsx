"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { User, FileText, ClipboardCheck, Calendar, Clock, AlertTriangle } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface HRGeneralistWorkspaceProps {
  activeItem: WorkspaceItem | null
  editMode: boolean
}

export function HRGeneralistWorkspace({ activeItem, editMode }: HRGeneralistWorkspaceProps) {
  // This is a simplified implementation
  // In a real application, we would have more complex logic based on the item type

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Employee Count</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">247</div>
            <p className="text-xs text-muted-foreground">+3 this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Open Positions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">4 in final stages</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Compliance Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-2xl font-bold mr-2">98%</div>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Compliant
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">2 items need attention</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="employees">Employees</TabsTrigger>
          <TabsTrigger value="policies">Policies</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <h2 className="text-xl font-semibold mt-4">Recent Activity</h2>

          <div className="space-y-2">
            <ActivityItem
              icon={<User className="h-4 w-4" />}
              title="New employee onboarded"
              description="Sarah Johnson - Marketing Specialist"
              time="2 hours ago"
            />

            <ActivityItem
              icon={<FileText className="h-4 w-4" />}
              title="Policy updated"
              description="Remote Work Policy - v2.3"
              time="Yesterday"
            />

            <ActivityItem
              icon={<ClipboardCheck className="h-4 w-4" />}
              title="Compliance report submitted"
              description="Q2 Equal Employment Opportunity"
              time="3 days ago"
            />
          </div>

          <h2 className="text-xl font-semibold mt-6">Upcoming Deadlines</h2>

          <div className="space-y-2">
            <DeadlineItem title="Annual Performance Reviews" dueDate="June 30, 2023" priority="high" />

            <DeadlineItem title="Benefits Enrollment Period" dueDate="July 15, 2023" priority="medium" />

            <DeadlineItem title="Quarterly Compliance Report" dueDate="July 31, 2023" priority="high" />
          </div>
        </TabsContent>

        <TabsContent value="employees">
          <p className="text-muted-foreground py-4">Employee management content would go here</p>
        </TabsContent>

        <TabsContent value="policies">
          <p className="text-muted-foreground py-4">Policy management content would go here</p>
        </TabsContent>

        <TabsContent value="compliance">
          <p className="text-muted-foreground py-4">Compliance tracking content would go here</p>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface ActivityItemProps {
  icon: React.ReactNode
  title: string
  description: string
  time: string
}

function ActivityItem({ icon, title, description, time }: ActivityItemProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border">
      <div className="mr-3 mt-0.5 text-primary">{icon}</div>
      <div className="flex-1">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
      <div className="text-xs text-muted-foreground flex items-center">
        <Clock className="h-3 w-3 mr-1" />
        {time}
      </div>
    </div>
  )
}

interface DeadlineItemProps {
  title: string
  dueDate: string
  priority: "low" | "medium" | "high"
}

function DeadlineItem({ title, dueDate, priority }: DeadlineItemProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border">
      <div className="mr-3 mt-0.5">
        <Calendar className="h-4 w-4 text-primary" />
      </div>
      <div className="flex-1">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">Due: {dueDate}</p>
      </div>
      <div>
        <Badge
          variant="outline"
          className={
            priority === "high"
              ? "bg-red-50 text-red-700 border-red-200"
              : priority === "medium"
                ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                : "bg-green-50 text-green-700 border-green-200"
          }
        >
          {priority === "high" && (
            <>
              <AlertTriangle className="h-3 w-3 mr-1" />
              High
            </>
          )}
          {priority === "medium" && "Medium"}
          {priority === "low" && "Low"}
        </Badge>
      </div>
    </div>
  )
}

