"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { FileText, Users, MessageSquare, Clock, Star } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface RecruiterWorkspaceProps {
  activeItem: WorkspaceItem | null
  editMode: boolean
}

export function RecruiterWorkspace({ activeItem, editMode }: RecruiterWorkspaceProps) {
  // This is a simplified implementation
  // In a real application, we would have more complex logic based on the item type

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Open Positions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-forest-600">12</div>
            <p className="text-xs text-gray-500">+3 this month</p>
          </CardContent>
        </Card>

        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Active Candidates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-forest-600">48</div>
            <p className="text-xs text-gray-500">15 in final stages</p>
          </CardContent>
        </Card>

        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Time to Hire</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-forest-600">24 days</div>
            <p className="text-xs text-gray-500">-15% from last quarter</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="bg-cream-100 p-1">
          <TabsTrigger
            value="overview"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="jobs"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Jobs
          </TabsTrigger>
          <TabsTrigger
            value="candidates"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Candidates
          </TabsTrigger>
          <TabsTrigger
            value="analytics"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <h2 className="text-xl font-semibold mt-4 text-gray-800">Recent Activity</h2>

          <div className="space-y-2">
            <ActivityItem
              icon={<FileText className="h-4 w-4" />}
              title="New job description created"
              description="Senior Python Developer - Fintech"
              time="2 hours ago"
            />

            <ActivityItem
              icon={<Users className="h-4 w-4" />}
              title="New candidate profile"
              description="John Smith - Python Developer"
              time="Yesterday"
            />

            <ActivityItem
              icon={<MessageSquare className="h-4 w-4" />}
              title="Interview feedback submitted"
              description="Sarah Johnson - Marketing Specialist"
              time="3 days ago"
            />
          </div>

          <h2 className="text-xl font-semibold mt-6 text-gray-800">Top Candidates</h2>

          <div className="space-y-2">
            {activeItem?.type === "job_description" ? (
              <>
                <CandidateItem
                  name="Alex Rivera"
                  position="Senior Python Developer"
                  matchScore={92}
                  status="Screening"
                />

                <CandidateItem name="Maria Chen" position="Python Developer" matchScore={87} status="Applied" />

                <CandidateItem name="James Wilson" position="Senior Developer" matchScore={85} status="Applied" />
              </>
            ) : (
              <div className="p-4 text-center text-gray-500">Select a job description to see matching candidates</div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="jobs">
          <p className="text-gray-500 py-4">Job management content would go here</p>
        </TabsContent>

        <TabsContent value="candidates">
          <p className="text-gray-500 py-4">Candidate management content would go here</p>
        </TabsContent>

        <TabsContent value="analytics">
          <p className="text-gray-500 py-4">Recruiting analytics content would go here</p>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface ActivityItemProps {
  icon: React.ReactNode
  title: string
  description: string
  time: string
}

function ActivityItem({ icon, title, description, time }: ActivityItemProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow bg-white">
      <div className="mr-3 mt-0.5 text-forest-600">{icon}</div>
      <div className="flex-1">
        <h3 className="font-medium text-gray-800">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="text-xs text-gray-500 flex items-center">
        <Clock className="h-3 w-3 mr-1" />
        {time}
      </div>
    </div>
  )
}

interface CandidateItemProps {
  name: string
  position: string
  matchScore: number
  status: string
}

function CandidateItem({ name, position, matchScore, status }: CandidateItemProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow bg-white">
      <div className="mr-3 mt-0.5">
        <Users className="h-4 w-4 text-forest-600" />
      </div>
      <div className="flex-1">
        <h3 className="font-medium text-gray-800">{name}</h3>
        <p className="text-sm text-gray-600">{position}</p>
      </div>
      <div className="text-right">
        <div className="flex items-center">
          <Star className="h-3 w-3 text-amber-500 mr-1" />
          <span className="font-medium text-forest-600">{matchScore}%</span>
        </div>
        <Badge variant="outline" className="mt-1 bg-forest-100 text-forest-600 border-forest-200">
          {status}
        </Badge>
      </div>
    </div>
  )
}

