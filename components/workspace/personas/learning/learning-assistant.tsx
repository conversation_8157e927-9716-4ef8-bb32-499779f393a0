"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Award } from "lucide-react"
import type { WorkspaceItem } from "../../types"

interface LearningAssistantProps {
  activeItem: WorkspaceItem | null
}

export function LearningAssistant({ activeItem }: LearningAssistantProps) {
  return (
    <div className="p-4 space-y-4">
      <div className="flex items-start gap-2">
        <GraduationCap className="h-5 w-5 text-primary mt-0.5" />
        <p className="text-sm">
          {!activeItem && "Hello! I'm your Learning Assistant. How can I help with your L&D initiatives today?"}
          {activeItem?.itemType === "training_program" &&
            `I see you're working on the ${activeItem.title} program. Would you like me to suggest content or analyze effectiveness?`}
          {activeItem?.itemType === "learning_path" &&
            `Looking at the ${activeItem.title} learning path. I can help with course recommendations or skill mapping.`}
          {activeItem?.itemType === "skills_assessment" &&
            `Reviewing the skills assessment for ${activeItem.title}. Need help with gap analysis or development planning?`}
        </p>
      </div>

      <div>
        <h4 className="text-xs font-medium text-muted-foreground mb-2">Suggested Actions</h4>
        <div className="space-y-1">
          {!activeItem ? (
            <>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                Create new training program
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <GraduationCap className="h-4 w-4 mr-2 text-muted-foreground" />
                Design learning path
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <BarChart className="h-4 w-4 mr-2 text-muted-foreground" />
                Analyze training effectiveness
              </Button>
            </>
          ) : (
            <>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <Award className="h-4 w-4 mr-2 text-muted-foreground" />
                Generate completion certificates
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <BarChart className="h-4 w-4 mr-2 text-muted-foreground" />
                Analyze participant feedback
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-sm h-8 px-2 font-normal">
                <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                Recommend additional resources
              </Button>
            </>
          )}
        </div>
      </div>

      <div>
        <h4 className="text-xs font-medium text-muted-foreground mb-2">Learning Insights</h4>
        <div className="bg-muted/50 p-3 rounded-md text-sm">
          {!activeItem ? (
            <p>
              Your most popular course has an 83% completion rate, which is 15% above industry average. Technical skills
              training shows the highest engagement across departments.
            </p>
          ) : (
            <p>
              Based on participant feedback, this program has a satisfaction score of 4.7/5. The hands-on exercises were
              rated as the most valuable component.
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

