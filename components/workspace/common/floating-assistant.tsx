"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Bot } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import type { WorkspaceItem, WorkspacePersona, Client } from "../types"

// Import persona-specific assistant components
import { RecruiterAssistant } from "../personas/recruiter/recruiter-assistant"
import { HRGeneralistAssistant } from "../personas/hr-generalist/hr-generalist-assistant"
import { BenefitsAssistant } from "../personas/benefits/benefits-assistant"
import { LearningAssistant } from "../personas/learning/learning-assistant"

interface FloatingAssistantProps {
  activeItem: WorkspaceItem | null
  activePersona: WorkspacePersona
  activeClient?: Client | null
}

export function FloatingAssistant({ activeItem, activePersona, activeClient }: FloatingAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [position, setPosition] = useState({ right: 20, bottom: 30 })
  const [isHovered, setIsHovered] = useState(false)
  const [hasNewSuggestion, setHasNewSuggestion] = useState(false)
  const [input, setInput] = useState("")

  // Get the assistant name based on persona
  const getAssistantName = () => {
    switch (activePersona) {
      case "recruiter":
        return "Recruiting Assistant"
      case "hr-generalist":
        return "HR Assistant"
      case "benefits":
        return "Benefits Assistant"
      case "learning":
        return "Learning Assistant"
      default:
        return "AI Assistant"
    }
  }

  // Render the appropriate assistant content based on persona
  const renderAssistantContent = () => {
    switch (activePersona) {
      case "recruiter":
        return <RecruiterAssistant activeItem={activeItem} activeClient={activeClient} />
      case "hr-generalist":
        return <HRGeneralistAssistant activeItem={activeItem} />
      case "benefits":
        return <BenefitsAssistant activeItem={activeItem} />
      case "learning":
        return <LearningAssistant activeItem={activeItem} />
      default:
        return <div>Select a workspace to get personalized assistance</div>
    }
  }

  // Handle sending a message
  const handleSendMessage = () => {
    if (input.trim()) {
      // In a real implementation, this would send the message to an AI service
      console.log(`Sending message to ${activePersona} assistant:`, input)
      setInput("")
    }
  }

  return (
    <>
      {/* Floating button */}
      <Button
        data-assistant="true"
        onClick={() => {
          setIsOpen(!isOpen)
          setHasNewSuggestion(false)
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={cn(
          "fixed rounded-full w-12 h-12 shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50",
          hasNewSuggestion && !isOpen
            ? "animate-pulse bg-forest-600 ring-2 ring-forest-600 ring-offset-2"
            : "bg-forest-600",
          isHovered && "scale-110",
        )}
        style={{
          right: `${position.right}px`,
          bottom: `${position.bottom}px`,
        }}
      >
        <Bot className="h-6 w-6 text-white" />
        {hasNewSuggestion && !isOpen && (
          <span className="absolute top-0 right-0 h-3 w-3 rounded-full bg-red-500"></span>
        )}
      </Button>

      {/* Assistant popup */}
      {isOpen && (
        <Card
          data-assistant="true"
          className="fixed z-50 w-80 h-[450px] flex flex-col shadow-forest rounded-xl overflow-hidden animate-in slide-in-from-right-10 duration-200 border border-gray-200/50 bg-white"
          style={{
            right: `${position.right + 60}px`,
            bottom: `${position.bottom}px`,
          }}
        >
          <div className="p-3 border-b border-gray-200/50 flex justify-between items-center bg-forest-600">
            <div className="flex items-center">
              <Sparkles className="h-4 w-4 mr-2 text-white" />
              <h3 className="font-medium text-white">{getAssistantName()}</h3>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <ScrollArea className="flex-1 bg-white">{renderAssistantContent()}</ScrollArea>

          <div className="p-3 border-t border-gray-200/50 bg-white">
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Ask anything..."
                className="flex-1 h-8 text-sm border-gray-200 focus:border-forest-600 focus:ring-forest-600"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
              />
              <Button
                size="icon"
                className="h-8 w-8 bg-forest-600 text-white hover:bg-forest-700"
                onClick={handleSendMessage}
              >
                <Send className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </Card>
      )}
    </>
  )
}

