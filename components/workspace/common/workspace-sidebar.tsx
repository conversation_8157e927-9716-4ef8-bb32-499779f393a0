"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, Filter, Users, ClipboardCheck, HeartPulse, GraduationCap, BookOpen } from "lucide-react"
import { cn } from "@/lib/utils"
import type { WorkspaceItem, WorkspacePersona } from "../types"

interface WorkspaceSidebarProps {
  activePersona: WorkspacePersona
  activeItem: WorkspaceItem | null
  onItemSelect: (item: WorkspaceItem) => void
  searchQuery: string
}

export function WorkspaceSidebar({ activePersona, activeItem, onItemSelect, searchQuery }: WorkspaceSidebarProps) {
  // This is a simplified implementation
  // In a real application, we would fetch items based on the active persona

  // Mock items for each persona
  const getPersonaItems = (): WorkspaceItem[] => {
    if (activePersona === "recruiter") {
      return [
        {
          id: "1",
          title: "Senior Python Developer",
          content: "Job description content",
          tags: ["Technical", "Development"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Job Descriptions",
          persona: "recruiter",
          versions: [],
          type: "job_description",
        },
        {
          id: "2",
          title: "Python Developers in Bay Area",
          content: "Boolean search content",
          tags: ["Technical", "Search"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Boolean Searches",
          persona: "recruiter",
          versions: [],
          type: "boolean_search",
        },
      ]
    } else if (activePersona === "hr-generalist") {
      return [
        {
          id: "3",
          title: "Remote Work Policy",
          content: "Policy document content",
          tags: ["Policy", "Remote Work"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Policies",
          persona: "hr-generalist",
          versions: [],
          type: "policy_document",
        },
        {
          id: "4",
          title: "Q2 Compliance Report",
          content: "Compliance report content",
          tags: ["Compliance", "Quarterly"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Compliance",
          persona: "hr-generalist",
          versions: [],
          type: "compliance_report",
        },
      ]
    } else if (activePersona === "benefits") {
      return [
        {
          id: "5",
          title: "Health Insurance Plan 2023",
          content: "Benefits plan content",
          tags: ["Health", "Insurance"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Benefits Plans",
          persona: "benefits",
          versions: [],
          type: "benefits_plan",
        },
        {
          id: "6",
          title: "Open Enrollment Campaign",
          content: "Enrollment campaign content",
          tags: ["Enrollment", "Campaign"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Enrollment",
          persona: "benefits",
          versions: [],
          type: "enrollment_campaign",
        },
      ]
    } else if (activePersona === "learning") {
      return [
        {
          id: "7",
          title: "Leadership Training Program",
          content: "Training program content",
          tags: ["Leadership", "Training"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Training Programs",
          persona: "learning",
          versions: [],
          type: "training_program",
        },
        {
          id: "8",
          title: "Technical Skills Learning Path",
          content: "Learning path content",
          tags: ["Technical", "Learning Path"],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: "active",
          category: "Learning Paths",
          persona: "learning",
          versions: [],
          type: "learning_path",
        },
      ]
    }

    return []
  }

  const items = getPersonaItems()

  // Filter items based on search query
  const filteredItems = searchQuery
    ? items.filter(
        (item) =>
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase())),
      )
    : items

  return (
    <div className="px-2">
      {filteredItems.map((item) => (
        <Button
          key={item.id}
          variant="ghost"
          className={cn(
            "w-full justify-start mb-1 font-normal text-white/80 hover:bg-white/10 hover:text-white",
            activeItem?.id === item.id && "bg-white/20 text-white",
          )}
          onClick={() => onItemSelect(item)}
        >
          {/* Render appropriate icon based on persona and item type */}
          {activePersona === "recruiter" && item.category === "Job Descriptions" && (
            <FileText className="h-4 w-4 mr-2" />
          )}
          {activePersona === "recruiter" && item.category === "Boolean Searches" && <Filter className="h-4 w-4 mr-2" />}
          {activePersona === "recruiter" && item.category === "Candidates" && <Users className="h-4 w-4 mr-2" />}

          {activePersona === "hr-generalist" && item.category === "Policies" && <FileText className="h-4 w-4 mr-2" />}
          {activePersona === "hr-generalist" && item.category === "Compliance" && (
            <ClipboardCheck className="h-4 w-4 mr-2" />
          )}
          {activePersona === "hr-generalist" && item.category === "Employees" && <Users className="h-4 w-4 mr-2" />}

          {activePersona === "benefits" && item.category === "Benefits Plans" && (
            <HeartPulse className="h-4 w-4 mr-2" />
          )}
          {activePersona === "benefits" && item.category === "Enrollment" && <Users className="h-4 w-4 mr-2" />}

          {activePersona === "learning" && item.category === "Training Programs" && (
            <GraduationCap className="h-4 w-4 mr-2" />
          )}
          {activePersona === "learning" && item.category === "Learning Paths" && <BookOpen className="h-4 w-4 mr-2" />}

          <span className="truncate">{item.title}</span>
        </Button>
      ))}

      {filteredItems.length === 0 && (
        <div className="px-4 py-8 text-center">
          <p className="text-white/60">No items found</p>
        </div>
      )}
    </div>
  )
}

