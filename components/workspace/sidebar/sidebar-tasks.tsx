"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CalendarCheck } from "lucide-react"
import { cn } from "@/lib/utils"
import type { TaskType } from "../types"

interface SidebarTasksProps {
  tasks: TaskType[]
  onTaskSelect: (task: TaskType) => void
}

export function SidebarTasks({ tasks, onTaskSelect }: SidebarTasksProps) {
  return (
    <div className="px-2">
      <div className="mb-3">
        <Button variant="ghost" className="w-full justify-start mb-1 font-medium text-muted-foreground" disabled>
          <CalendarCheck className="h-4 w-4 mr-2" />
          <span>High Priority</span>
        </Button>

        {tasks
          .filter((task) => task.priority === "high")
          .map((task) => (
            <Button
              key={task.id}
              variant="ghost"
              className="w-full justify-start mb-1 font-normal pl-9 text-left"
              onClick={() => onTaskSelect(task)}
            >
              <div className="flex items-center space-x-2 w-full">
                <div className="h-2 w-2 rounded-full bg-destructive" />
                <span className="truncate">{task.title}</span>
                <span className="text-xs text-muted-foreground ml-auto">
                  {new Date(task.dueDate).toLocaleDateString()}
                </span>
              </div>
            </Button>
          ))}
      </div>

      <div className="mb-3">
        <Button variant="ghost" className="w-full justify-start mb-1 font-medium text-muted-foreground" disabled>
          <CalendarCheck className="h-4 w-4 mr-2" />
          <span>Upcoming Tasks</span>
        </Button>

        {tasks
          .filter((task) => task.status !== "completed")
          .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
          .map((task) => (
            <Button
              key={task.id}
              variant="ghost"
              className="w-full justify-start mb-1 font-normal pl-9 text-left"
              onClick={() => onTaskSelect(task)}
            >
              <div className="flex items-center space-x-2 w-full">
                <div
                  className={cn(
                    "h-2 w-2 rounded-full",
                    task.priority === "high"
                      ? "bg-destructive"
                      : task.priority === "medium"
                        ? "bg-amber-500"
                        : "bg-green-500",
                  )}
                />
                <span className="truncate">{task.title}</span>
                <span className="text-xs text-muted-foreground ml-auto">
                  {new Date(task.dueDate).toLocaleDateString()}
                </span>
              </div>
            </Button>
          ))}
      </div>

      <div className="mb-3">
        <Button variant="ghost" className="w-full justify-start mb-1 font-medium text-muted-foreground" disabled>
          <CalendarCheck className="h-4 w-4 mr-2" />
          <span>Completed</span>
        </Button>

        {tasks
          .filter((task) => task.status === "completed")
          .map((task) => (
            <Button
              key={task.id}
              variant="ghost"
              className="w-full justify-start mb-1 font-normal pl-9 text-left line-through text-muted-foreground"
              onClick={() => onTaskSelect(task)}
            >
              <div className="flex items-center space-x-2 w-full">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <span className="truncate">{task.title}</span>
              </div>
            </Button>
          ))}
      </div>
    </div>
  )
}

