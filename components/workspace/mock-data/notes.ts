import type { MeetingNote, PostingNote, SourcingNote, ScreeningNote, Note } from "../types"

export const meetingNotes: MeetingNote[] = [
  {
    id: "meeting-note-r1-4",
    taskId: "task-r1-4",
    type: "meeting",
    lastEdited: new Date(2023, 3, 20),
    title: "Engineering Leadership Interview Planning",
    date: new Date(2023, 3, 20),
    attendees: [
      "<PERSON> (Recruiter)",
      "<PERSON> (Recruiting Coordinator)",
      "<PERSON> (Engineering Director)",
      "<PERSON> (Backend Engineering Manager)"
    ],
    roleRequirements: [
      "Need to coordinate technical panel interviews for Senior Backend Engineers",
      "Each candidate should meet with at least 3 engineering leaders",
      "Focus on distributed systems experience and technical leadership",
      "Interview process should include system design and coding components"
    ],
    compensation: {
      budget: "$160,000 - $200,000 depending on experience",
      benefits: "Full benefits package, remote work options, and stock options"
    },
    timeline: {
      startDate: "Planning to schedule interviews over the next 2 weeks"
    },
    nextSteps: [
      "Emily to create interview schedule template",
      "Michael to prepare system design questions",
      "Lisa to organize coding challenge evaluation criteria",
      "<PERSON> to begin scheduling candidates for next week"
    ]
  },
  {
    id: "meeting-note-1",
    taskId: "task-1",
    type: "meeting",
    lastEdited: new Date(2023, 2, 5),
    title: "Client Meeting - Acme Financial",
    date: new Date(2023, 2, 5),
    attendees: [
      "<PERSON> Johnson (Acme Financial, CTO)",
      "<PERSON> (Acme Financial, Team Lead)",
      "Alex Rivera (Recruiter)",
    ],
    roleRequirements: [
      "Senior Python Developer with 5+ years experience in financial applications",
      "Strong experience with Django and Flask frameworks required",
      "Knowledge of financial data processing and analysis is essential",
      "Team will be 5-7 developers, hybrid work environment (3 days in office)",
    ],
    compensation: {
      budget: "$150,000 - $180,000 depending on experience",
      benefits: "Standard package plus annual performance bonus",
    },
    timeline: {
      startDate: "Aiming to have someone onboard within 6 weeks",
    },
    nextSteps: [
      "Create formal job description based on these requirements",
      "Send draft JD to Sarah for approval by Friday",
      "Begin sourcing candidates once JD is approved",
    ],
  },
]

export const postingNotes: PostingNote[] = [
  {
    id: "posting-note-r1-2",
    taskId: "task-r1-2",
    type: "posting",
    lastEdited: new Date(2023, 3, 5),
    title: "Senior Backend Engineer - Distributed Systems",
    overview:
      "We are seeking experienced Backend Engineers with strong distributed systems knowledge to join our growing engineering team. You'll be working on highly-scalable systems that process millions of transactions daily.",
    company: "O6 Technologies",
    location: "Remote (US) or San Francisco, CA",
    salaryRange: "$160,000 - $200,000",
    experience: "5+ years",
    responsibilities: [
      "Design and implement scalable, fault-tolerant distributed systems",
      "Build and maintain high-performance backend services",
      "Collaborate with product and platform teams on architectural decisions",
      "Mentor junior engineers and contribute to engineering best practices"
    ]
  },
  {
    id: "posting-note-1",
    taskId: "task-2",
    type: "posting",
    lastEdited: new Date(2023, 2, 10),
    title: "Senior Python Developer - Fintech",
    overview:
      "We are seeking a highly skilled and motivated Senior Python Developer to join our dynamic Fintech team. The ideal candidate will have a strong background in software development with Python, a passion for financial technology, and the ability to solve complex problems.",
    company: "Acme Financial",
    location: "San Francisco, CA (Hybrid)",
    salaryRange: "$150,000 - $180,000",
    experience: "5+ years",
    responsibilities: [
      "Developing and maintaining Python-based applications for financial data processing and analysis.",
      "Writing clean, efficient, and reusable code following best practices in the fintech industry.",
      "Collaborating with cross-functional teams, including data scientists, product managers, and other developers.",
    ],
  },
]

export const sourcingNotes: SourcingNote[] = [
  {
    id: "sourcing-note-r1-6",
    taskId: "task-r1-6",
    type: "sourcing",
    lastEdited: new Date(2023, 3, 18),
    title: "Frontend Developer Communities Outreach",
    booleanString: `(react OR reactjs OR "react.js" OR nextjs OR "next.js") AND (frontend OR "front end" OR "front-end") AND (senior OR sr OR lead) AND ("full stack" OR fullstack) -wordpress -shopify`,
    analysis:
      "Targeting experienced frontend developers with React and Next.js skills. Focusing on professional communities, conference speakers, and open source contributors.",
    expectedResults: "Plan to reach out to 50-75 developers from React conferences, 30-40 from GitHub contributors, and 20-30 from tech meetup groups."
  },
  {
    id: "sourcing-note-1",
    taskId: "task-3",
    type: "sourcing",
    lastEdited: new Date(2023, 2, 15),
    title: "Boolean Search - Python Developers in Bay Area",
    booleanString: `(python OR "python developer") AND (django OR flask) AND (senior OR sr OR lead) AND ("bay area" OR san francisco OR "san jose" OR oakland) -junior -intern`,
    analysis:
      "This search string targets experienced Python developers in the Bay Area with experience in Django or Flask frameworks. It excludes junior positions and internships.",
    expectedResults: "Estimated 200-300 matches on LinkedIn, 150-200 on Indeed, and 100-150 on Dice.",
  },
]

export const screeningNotes: ScreeningNote[] = [
  {
    id: "screening-note-r1-3",
    taskId: "task-r1-3",
    type: "screening",
    lastEdited: new Date(2023, 3, 16),
    candidateName: "Alex Chen",
    professionalSummary:
      "Senior Backend Engineer with 7 years of experience building distributed systems. Strong expertise in Kubernetes, microservices architecture, and cloud infrastructure. Previously at Stripe and Uber.",
    skills: [
      { name: "golang", rating: 9 },
      { name: "kubernetes", rating: 8 },
      { name: "microservices", rating: 9 },
      { name: "aws", rating: 8 },
      { name: "system design", rating: 9 },
      { name: "java", rating: 7 }
    ],
    screeningNotes:
      "Alex is an exceptional candidate with deep distributed systems knowledge. His experience at Stripe building their payment processing infrastructure is directly relevant to our needs. Currently making $180k base with bonus and equity bringing total comp to $240k. Expecting at least $190k base with comparable total comp. Available to start in 4 weeks after notice period."
  },
  {
    id: "screening-note-1",
    taskId: "task-4",
    type: "screening",
    lastEdited: new Date(2023, 2, 20),
    candidateName: "John Smith",
    professionalSummary:
      "Experienced Python Developer with 8 years of experience building web applications and data processing systems. Strong expertise in Django and Flask frameworks, with a background in fintech applications.",
    skills: [
      { name: "python", rating: 9 },
      { name: "django", rating: 8 },
      { name: "flask", rating: 8 },
      { name: "sql", rating: 7 },
      { name: "react", rating: 6 },
      { name: "aws", rating: 7 },
    ],
    screeningNotes:
      "John demonstrates strong technical skills and has relevant experience in fintech. He seems particularly interested in data processing applications and has experience with financial APIs. Currently making $145k and looking for at least $160k.",
  },
]

export const allNotes = [...meetingNotes, ...postingNotes, ...sourcingNotes, ...screeningNotes]

export function getNoteByTaskId(taskId: string): Note | undefined {
  return allNotes.find((note) => note.taskId === taskId)
}

