import type { Job } from "../types"

export const mockJobs: Job[] = [
  {
    id: "job-1",
    title: "Senior Python Developer - Fintech",
    content: "We are seeking a highly skilled and motivated Python Developer...",
    tags: ["Python", "Fintech", "Senior"],
    createdAt: new Date("2023-03-15"),
    updatedAt: new Date("2023-03-18"),
    type: "job_description",
    status: "active",
    category: "Job Descriptions",
    clientId: "client-1", // Acme Corporation
    department: "Engineering",
    location: "San Francisco, CA",
    salary: "$150,000 - $180,000",
    experience: "5+ years",
    persona: "recruiter",
    versions: [
      {
        id: "v1",
        content: "Initial draft of Python Developer job description",
        createdAt: new Date("2023-03-15"),
      },
      {
        id: "v2",
        content: "Updated Python Developer job description with additional requirements",
        createdAt: new Date("2023-03-18"),
      },
    ],
  },
  {
    id: "job-2",
    title: "Frontend React Developer",
    content: "Looking for a skilled React developer to join our team...",
    tags: ["React", "JavaScript", "Frontend"],
    createdAt: new Date("2023-04-10"),
    updatedAt: new Date("2023-04-12"),
    type: "job_description",
    status: "active",
    category: "Job Descriptions",
    clientId: "client-1", // Acme Corporation
    department: "Engineering",
    location: "Remote",
    salary: "$120,000 - $150,000",
    experience: "3+ years",
    persona: "recruiter",
    versions: [
      {
        id: "v1",
        content: "Initial draft of React Developer job description",
        createdAt: new Date("2023-04-10"),
      },
    ],
  },
  {
    id: "job-3",
    title: "DevOps Engineer",
    content: "Seeking a DevOps engineer with experience in AWS and CI/CD pipelines...",
    tags: ["DevOps", "AWS", "CI/CD"],
    createdAt: new Date("2023-05-05"),
    updatedAt: new Date("2023-05-07"),
    type: "job_description",
    status: "active",
    category: "Job Descriptions",
    clientId: "client-1", // Acme Corporation
    department: "Operations",
    location: "San Francisco, CA",
    salary: "$140,000 - $170,000",
    experience: "4+ years",
    persona: "recruiter",
    versions: [
      {
        id: "v1",
        content: "Initial draft of DevOps Engineer job description",
        createdAt: new Date("2023-05-05"),
      },
    ],
  },
  {
    id: "job-4",
    title: "Manufacturing Engineer",
    content: "Looking for a manufacturing engineer with experience in automotive processes...",
    tags: ["Manufacturing", "Automotive", "Engineering"],
    createdAt: new Date("2023-04-20"),
    updatedAt: new Date("2023-04-22"),
    type: "job_description",
    status: "active",
    category: "Job Descriptions",
    clientId: "client-2", // Globex Industries
    department: "Engineering",
    location: "Chicago, IL",
    salary: "$110,000 - $130,000",
    experience: "3+ years",
    persona: "recruiter",
    versions: [
      {
        id: "v1",
        content: "Initial draft of Manufacturing Engineer job description",
        createdAt: new Date("2023-04-20"),
      },
    ],
  },
  {
    id: "job-5",
    title: "Quality Assurance Specialist",
    content: "Seeking a QA specialist with experience in manufacturing quality control...",
    tags: ["QA", "Manufacturing", "Quality Control"],
    createdAt: new Date("2023-05-15"),
    updatedAt: new Date("2023-05-17"),
    type: "job_description",
    status: "active",
    category: "Job Descriptions",
    clientId: "client-2", // Globex Industries
    department: "Quality",
    location: "Chicago, IL",
    salary: "$90,000 - $110,000",
    experience: "2+ years",
    persona: "recruiter",
    versions: [
      {
        id: "v1",
        content: "Initial draft of QA Specialist job description",
        createdAt: new Date("2023-05-15"),
      },
    ],
  },
  {
    id: "job-6",
    title: "Aerospace Engineer",
    content: "Looking for an aerospace engineer with experience in propulsion systems...",
    tags: ["Aerospace", "Engineering", "Propulsion"],
    createdAt: new Date("2023-06-10"),
    updatedAt: new Date("2023-06-12"),
    type: "job_description",
    status: "active",
    category: "Job Descriptions",
    clientId: "client-4", // Stark Industries
    department: "R&D",
    location: "Los Angeles, CA",
    salary: "$160,000 - $200,000",
    experience: "7+ years",
    persona: "recruiter",
    versions: [
      {
        id: "v1",
        content: "Initial draft of Aerospace Engineer job description",
        createdAt: new Date("2023-06-10"),
      },
    ],
  },
]

