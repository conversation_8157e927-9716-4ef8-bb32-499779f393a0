import type { Program, WorkspacePersona } from "../types"

export const programs: Program[] = [
  {
    id: "prog-1",
    title: "Q1 Marketing Campaign Launch",
    description: "Comprehensive marketing campaign to launch our new product line in Q1",
    quarter: "Q1",
    year: 2023,
    status: "active",
    progress: 65,
    owner: "<PERSON>",
    createdAt: new Date(2023, 0, 15), // Jan 15
    updatedAt: new Date(2023, 1, 10), // Feb 10
    persona: "hr-generalist",
  },
  {
    id: "prog-2",
    title: "Q2 Product Enhancement",
    description: "Major product enhancements and feature additions planned for Q2",
    quarter: "Q2",
    year: 2023,
    status: "planning",
    progress: 25,
    owner: "<PERSON>",
    createdAt: new Date(2023, 2, 5), // Mar 5
    updatedAt: new Date(2023, 2, 20), // Mar 20
    persona: "hr-generalist",
  },
  // Recruiting-focused Programs
  {
    id: "prog-r1",
    title: "Engineering Talent Acquisition",
    description: "Strategic initiative to expand our engineering team with top talent",
    quarter: "Q1",
    year: 2025,
    status: "active",
    progress: 45,
    owner: "<PERSON>",
    createdAt: new Date(2025, 0, 15), // Jan 15
    updatedAt: new Date(2025, 0, 25), // Jan 25
    persona: "recruiter",
  },
  {
    id: "prog-r2",
    title: "Product Team Expansion",
    description: "Recruiting initiative to grow our product management and design teams",
    quarter: "Q1",
    year: 2025,
    status: "active",
    progress: 30,
    owner: "David Wilson",
    createdAt: new Date(2025, 0, 10), // Jan 10
    updatedAt: new Date(2025, 0, 20), // Jan 20
    persona: "recruiter",
  },
  {
    id: "prog-r3",
    title: "Sales Team Development",
    description: "Program to recruit and onboard new sales representatives for our growing markets",
    quarter: "Q1",
    year: 2025,
    status: "planning",
    progress: 15,
    owner: "Emily Rodriguez",
    createdAt: new Date(2025, 0, 5), // Jan 5
    updatedAt: new Date(2025, 0, 15), // Jan 15
    persona: "recruiter",
  },
  {
    id: "prog-3",
    title: "Q1 Talent Acquisition",
    description: "Strategic hiring initiative to expand engineering and product teams",
    quarter: "Q1",
    year: 2023,
    status: "active",
    progress: 80,
    owner: "Sarah Johnson",
    createdAt: new Date(2023, 0, 10), // Jan 10
    updatedAt: new Date(2023, 2, 15), // Mar 15
    persona: "recruiter",
  },
  {
    id: "prog-4",
    title: "Q2 Employee Wellness Program",
    description: "Comprehensive wellness program to improve employee health and satisfaction",
    quarter: "Q2",
    year: 2023,
    status: "planning",
    progress: 15,
    owner: "David Wilson",
    createdAt: new Date(2023, 2, 10), // Mar 10
    updatedAt: new Date(2023, 2, 25), // Mar 25
    persona: "benefits",
  },
  {
    id: "prog-5",
    title: "Q1 Leadership Development",
    description: "Training program to develop leadership skills among managers",
    quarter: "Q1",
    year: 2023,
    status: "completed",
    progress: 100,
    owner: "Emily Rodriguez",
    createdAt: new Date(2023, 0, 5), // Jan 5
    updatedAt: new Date(2023, 2, 28), // Mar 28
    persona: "learning",
  },
]

export function getMockPrograms(persona?: WorkspacePersona): Program[] {
  // Filter by persona if provided
  if (persona) {
    return programs.filter((program) => program.persona === persona)
  }

  return programs
}

// Add a function for client-specific programs
export function getClientMockPrograms(): Program[] {
  return [
    // Client engagement programs
    {
      id: "client-prog-1",
      title: "ABC Corp Account Expansion",
      description: "Strategic account expansion for ABC Corporation",
      quarter: "Q1",
      year: 2025,
      status: "active",
      progress: 65,
      owner: "Sarah Johnson",
      createdAt: new Date(2025, 0, 5), // Jan 5, 2025
      updatedAt: new Date(2025, 0, 15), // Jan 15, 2025
      persona: "recruiter",
    },
    {
      id: "client-prog-2",
      title: "XYZ Inc. New Contract",
      description: "New client engagement with XYZ Inc.",
      quarter: "Q1",
      year: 2025,
      status: "planning",
      progress: 25,
      owner: "David Wilson",
      createdAt: new Date(2025, 0, 10), // Jan 10, 2025
      updatedAt: new Date(2025, 0, 20), // Jan 20, 2025
      persona: "recruiter",
    },
    {
      id: "client-prog-3",
      title: "Tech Innovators Partnership",
      description: "Strategic partnership with Tech Innovators LLC",
      quarter: "Q1",
      year: 2025,
      status: "active",
      progress: 40,
      owner: "Emily Rodriguez",
      createdAt: new Date(2025, 0, 15), // Jan 15, 2025
      updatedAt: new Date(2025, 0, 25), // Jan 25, 2025
      persona: "recruiter",
    },
  ];
}

