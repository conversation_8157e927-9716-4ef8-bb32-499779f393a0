import type { Client } from "../types"

export const mockClients: Client[] = [
  {
    id: "client-1",
    orgId: "org-1",
    name: "Acme Corporation",
    industry: "Technology",
    location: "San Francisco, CA",
    contactPerson: "<PERSON>",
    contactEmail: "<EMAIL>",
    contactPhone: "(*************",
    status: "active",
    notes: "Long-term client with multiple technical positions",
    createdAt: new Date(2023, 0, 15),
    updatedAt: new Date(2023, 5, 10),
    openJobs: 3,
    totalPlacements: 12,
  },
  {
    id: "client-2",
    orgId: "org-1",
    name: "Globex Industries",
    industry: "Manufacturing",
    location: "Chicago, IL",
    contactPerson: "<PERSON>",
    contactEmail: "<EMAIL>",
    contactPhone: "(*************",
    status: "active",
    notes: "Expanding engineering department",
    createdAt: new Date(2023, 2, 5),
    updatedAt: new Date(2023, 4, 20),
    openJobs: 2,
    totalPlacements: 5,
  },
  {
    id: "client-3",
    orgId: "org-1",
    name: "Initech Systems",
    industry: "Finance",
    location: "New York, NY",
    contactPerson: "Michael Bolton",
    contactEmail: "<EMAIL>",
    contactPhone: "(*************",
    status: "inactive",
    notes: "On hold until next quarter",
    createdAt: new Date(2022, 11, 10),
    updatedAt: new Date(2023, 3, 15),
    openJobs: 0,
    totalPlacements: 8,
  },
  {
    id: "client-4",
    orgId: "org-1",
    name: "Stark Industries",
    industry: "Defense",
    location: "Los Angeles, CA",
    contactPerson: "Tony Stark",
    contactEmail: "<EMAIL>",
    contactPhone: "(*************",
    status: "active",
    notes: "Looking for top engineering talent",
    createdAt: new Date(2023, 1, 20),
    updatedAt: new Date(2023, 6, 5),
    openJobs: 5,
    totalPlacements: 3,
  },
  {
    id: "client-5",
    orgId: "org-1",
    name: "Wayne Enterprises",
    industry: "Conglomerate",
    location: "Gotham City",
    contactPerson: "Bruce Wayne",
    contactEmail: "<EMAIL>",
    contactPhone: "(*************",
    status: "prospect",
    notes: "Initial discussions for IT department staffing",
    createdAt: new Date(2023, 5, 25),
    updatedAt: new Date(2023, 5, 25),
    openJobs: 0,
    totalPlacements: 0,
  },
]

