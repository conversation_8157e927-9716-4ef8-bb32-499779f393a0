import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Copy } from "lucide-react"
import type { BlockType, WorkspaceItem } from "../types"

interface BlockViewerProps {
  blocks: BlockType[]
  activeItem: WorkspaceItem
}

export function BlockViewer({ blocks, activeItem }: BlockViewerProps) {
  return (
    <div className="space-y-4">
      {blocks.map((block) => (
        <div key={block.id} className="py-1">
          {block.type === "heading" && <h1 className="text-3xl font-bold">{block.content}</h1>}
          {block.type === "subheading" && <h2 className="text-xl font-semibold mt-6">{block.content}</h2>}
          {block.type === "text" && <p className="text-base">{block.content}</p>}
          {block.type === "bullet" && (
            <div className="flex items-start">
              <span className="mr-2 mt-1.5">•</span>
              <p>{block.content}</p>
            </div>
          )}
          {block.type === "numbered" && (
            <div className="flex items-start">
              <span className="mr-2 font-medium">
                {blocks.filter((b) => b.type === "numbered").indexOf(block) + 1}.
              </span>
              <p>{block.content}</p>
            </div>
          )}
          {block.type === "job_details" && (
            <Card className="p-4 bg-muted/50 my-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Company</h3>
                  <p>{JSON.parse(block.content).company || "Not specified"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Location</h3>
                  <p>{JSON.parse(block.content).location || "Not specified"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Salary Range</h3>
                  <p>{JSON.parse(block.content).salary || "Not specified"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Experience</h3>
                  <p>{JSON.parse(block.content).experience || "Not specified"}</p>
                </div>
              </div>
            </Card>
          )}
          {block.type === "boolean_search" && (
            <Card className="p-4 bg-muted/50 my-4">
              <h3 className="text-sm font-medium mb-2">Boolean Search String</h3>
              <pre className="bg-muted p-3 rounded-md overflow-x-auto text-sm font-mono whitespace-pre-wrap">
                {block.content}
              </pre>
              <div className="flex justify-end mt-2">
                <Button variant="outline" size="sm">
                  <Copy className="h-3 w-3 mr-1" />
                  Copy to Clipboard
                </Button>
              </div>
            </Card>
          )}
          {block.type === "candidate_rating" && (
            <Card className="p-4 bg-muted/50 my-4">
              <h3 className="text-sm font-medium mb-2">Skills Assessment</h3>
              <div className="space-y-2">
                {Object.entries(JSON.parse(block.content)).map(([skill, rating]) => (
                  <div key={skill} className="flex items-center">
                    <span className="w-1/3 text-sm">{skill}</span>
                    <div className="flex-1 mx-2 bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full"
                        style={{ width: `${(rating as number) * 10}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{rating}/10</span>
                  </div>
                ))}
              </div>
            </Card>
          )}
          {block.type === "divider" && <Separator className="my-4" />}
        </div>
      ))}
    </div>
  )
}

