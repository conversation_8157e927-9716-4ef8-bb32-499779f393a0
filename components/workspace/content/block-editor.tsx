"use client"

import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { X } from "lucide-react"
import type { BlockType } from "../types"

interface BlockEditorProps {
  blocks: BlockType[]
  onUpdateBlock: (id: string, content: string) => void
  onDeleteBlock: (id: string) => void
  onAddBlock: (type: BlockType["type"]) => void
  activeItemType?: string
}

export function BlockEditor({ blocks, onUpdateBlock, onDeleteBlock, onAddBlock, activeItemType }: BlockEditorProps) {
  return (
    <div className="space-y-4">
      {blocks.map((block) => (
        <div key={block.id} className="group relative border border-transparent hover:border-border rounded-md p-2">
          {block.type === "heading" && (
            <Input
              value={block.content}
              onChange={(e) => onUpdateBlock(block.id, e.target.value)}
              className="text-2xl font-bold border-none focus-visible:ring-0 p-0"
              placeholder="Heading"
            />
          )}

          {block.type === "subheading" && (
            <Input
              value={block.content}
              onChange={(e) => onUpdateBlock(block.id, e.target.value)}
              className="text-xl font-semibold border-none focus-visible:ring-0 p-0"
              placeholder="Subheading"
            />
          )}

          {block.type === "text" && (
            <textarea
              value={block.content}
              onChange={(e) => onUpdateBlock(block.id, e.target.value)}
              className="w-full min-h-[100px] border-none focus:outline-none p-0 resize-none bg-transparent"
              placeholder="Start typing..."
            />
          )}

          {block.type === "bullet" && (
            <div className="flex items-start">
              <span className="mr-2 mt-1.5">•</span>
              <textarea
                value={block.content}
                onChange={(e) => onUpdateBlock(block.id, e.target.value)}
                className="w-full min-h-[60px] border-none focus:outline-none p-0 resize-none bg-transparent"
                placeholder="Bullet point"
              />
            </div>
          )}

          {block.type === "numbered" && (
            <div className="flex items-start">
              <span className="mr-2 font-medium">1.</span>
              <textarea
                value={block.content}
                onChange={(e) => onUpdateBlock(block.id, e.target.value)}
                className="w-full min-h-[60px] border-none focus:outline-none p-0 resize-none bg-transparent"
                placeholder="Numbered item"
              />
            </div>
          )}

          {block.type === "job_details" && (
            <Card className="p-4 bg-muted/50">
              <h3 className="text-sm font-medium mb-2">Job Details</h3>
              <div className="grid grid-cols-2 gap-2">
                <Input
                  placeholder="Company"
                  value={JSON.parse(block.content).company || ""}
                  onChange={(e) => {
                    const current = JSON.parse(block.content)
                    onUpdateBlock(
                      block.id,
                      JSON.stringify({
                        ...current,
                        company: e.target.value,
                      }),
                    )
                  }}
                  className="text-sm"
                />
                <Input
                  placeholder="Location"
                  value={JSON.parse(block.content).location || ""}
                  onChange={(e) => {
                    const current = JSON.parse(block.content)
                    onUpdateBlock(
                      block.id,
                      JSON.stringify({
                        ...current,
                        location: e.target.value,
                      }),
                    )
                  }}
                  className="text-sm"
                />
                <Input
                  placeholder="Salary Range"
                  value={JSON.parse(block.content).salary || ""}
                  onChange={(e) => {
                    const current = JSON.parse(block.content)
                    onUpdateBlock(
                      block.id,
                      JSON.stringify({
                        ...current,
                        salary: e.target.value,
                      }),
                    )
                  }}
                  className="text-sm"
                />
                <Input
                  placeholder="Experience"
                  value={JSON.parse(block.content).experience || ""}
                  onChange={(e) => {
                    const current = JSON.parse(block.content)
                    onUpdateBlock(
                      block.id,
                      JSON.stringify({
                        ...current,
                        experience: e.target.value,
                      }),
                    )
                  }}
                  className="text-sm"
                />
              </div>
            </Card>
          )}

          {block.type === "boolean_search" && (
            <Card className="p-4 bg-muted/50">
              <h3 className="text-sm font-medium mb-2">Boolean Search String</h3>
              <textarea
                value={block.content}
                onChange={(e) => onUpdateBlock(block.id, e.target.value)}
                className="w-full min-h-[100px] text-sm font-mono border-none focus:outline-none p-2 bg-muted rounded-md"
                placeholder="Enter your boolean search string"
              />
            </Card>
          )}

          {block.type === "candidate_rating" && (
            <Card className="p-4 bg-muted/50">
              <h3 className="text-sm font-medium mb-2">Skills Assessment</h3>
              <div className="space-y-2">
                {Object.entries(JSON.parse(block.content)).map(([skill, rating]) => (
                  <div key={skill} className="flex items-center">
                    <Input
                      value={skill}
                      onChange={(e) => {
                        const current = JSON.parse(block.content)
                        const newContent = Object.entries(current).reduce(
                          (acc, [k, v]) => {
                            if (k === skill) {
                              acc[e.target.value] = v
                            } else {
                              acc[k] = v
                            }
                            return acc
                          },
                          {} as Record<string, number>,
                        )
                        onUpdateBlock(block.id, JSON.stringify(newContent))
                      }}
                      className="text-sm w-1/3"
                    />
                    <Input
                      type="range"
                      min="0"
                      max="10"
                      value={rating as number}
                      onChange={(e) => {
                        const current = JSON.parse(block.content)
                        onUpdateBlock(
                          block.id,
                          JSON.stringify({
                            ...current,
                            [skill]: Number.parseInt(e.target.value),
                          }),
                        )
                      }}
                      className="mx-2"
                    />
                    <span className="text-sm font-medium">{rating}/10</span>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const current = JSON.parse(block.content)
                    onUpdateBlock(
                      block.id,
                      JSON.stringify({
                        ...current,
                        [`Skill ${Object.keys(current).length + 1}`]: 5,
                      }),
                    )
                  }}
                >
                  Add Skill
                </Button>
              </div>
            </Card>
          )}

          <Button
            variant="ghost"
            size="icon"
            className="absolute -right-2 -top-2 opacity-0 group-hover:opacity-100 focus:opacity-100"
            onClick={() => onDeleteBlock(block.id)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ))}
      <div className="flex items-center space-x-2 mt-4 p-2 border border-dashed border-border rounded-md">
        <Button variant="ghost" size="sm" onClick={() => onAddBlock("text")}>
          Text
        </Button>
        <Button variant="ghost" size="sm" onClick={() => onAddBlock("heading")}>
          Heading
        </Button>
        <Button variant="ghost" size="sm" onClick={() => onAddBlock("subheading")}>
          Subheading
        </Button>
        <Button variant="ghost" size="sm" onClick={() => onAddBlock("bullet")}>
          Bullet
        </Button>
        <Button variant="ghost" size="sm" onClick={() => onAddBlock("numbered")}>
          Numbered
        </Button>

        {activeItemType === "job_description" && (
          <Button variant="ghost" size="sm" onClick={() => onAddBlock("job_details")}>
            Job Details
          </Button>
        )}

        {activeItemType === "boolean_search" && (
          <Button variant="ghost" size="sm" onClick={() => onAddBlock("boolean_search")}>
            Search String
          </Button>
        )}

        {activeItemType === "candidate_profile" && (
          <Button variant="ghost" size="sm" onClick={() => onAddBlock("candidate_rating")}>
            Skills Rating
          </Button>
        )}
      </div>
    </div>
  )
}

