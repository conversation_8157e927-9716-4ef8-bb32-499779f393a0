"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, Filter, Users, BriefcaseBusiness } from "lucide-react"

interface WelcomeScreenProps {
  onCreateItem: (type: string) => void
}

export function WelcomeScreen({ onCreateItem }: WelcomeScreenProps) {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="text-center">
        <BriefcaseBusiness className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-medium">Welcome to Your Workspace</h3>
        <p className="text-muted-foreground mt-2 max-w-md">
          Manage your job descriptions, search strings, candidate profiles, and tasks all in one place.
        </p>
        <div className="mt-6 flex flex-wrap justify-center gap-2">
          <Button onClick={() => onCreateItem("job_description")}>
            <FileText className="h-4 w-4 mr-2" />
            Create Job Description
          </Button>
          <Button variant="outline" onClick={() => onCreateItem("boolean_search")}>
            <Filter className="h-4 w-4 mr-2" />
            Create Boolean Search
          </Button>
          <Button variant="outline" onClick={() => onCreateItem("candidate_profile")}>
            <Users className="h-4 w-4 mr-2" />
            Add Candidate Profile
          </Button>
        </div>
      </div>
    </div>
  )
}

