"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Edit, Save, X } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import type { Note } from "../../types"

interface PostingNoteTemplateProps {
  note: Note
  onSave?: (note: Note) => void
}

export function PostingNoteTemplate({ note, onSave }: PostingNoteTemplateProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedNote, setEditedNote] = useState<Note>(note)

  const handleSave = () => {
    setIsEditing(false)
    if (onSave) {
      onSave(editedNote)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditedNote(note)
  }

  const updateResponsibility = (index: number, value: string) => {
    const updatedResponsibilities = [...editedNote.content.responsibilities]
    updatedResponsibilities[index] = value
    setEditedNote({
      ...editedNote,
      content: {
        ...editedNote.content,
        responsibilities: updatedResponsibilities,
      },
    })
  }

  return (
    <Card className="mt-6">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-xl font-semibold">{note.content.title}</CardTitle>
        {isEditing ? (
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleCancel}>
              <X className="h-4 w-4 mr-1" /> Cancel
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="h-4 w-4 mr-1" /> Save
            </Button>
          </div>
        ) : (
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-1" /> Edit
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Overview</h3>
            {isEditing ? (
              <Textarea
                value={editedNote.content.overview}
                onChange={(e) =>
                  setEditedNote({
                    ...editedNote,
                    content: {
                      ...editedNote.content,
                      overview: e.target.value,
                    },
                  })
                }
                rows={4}
              />
            ) : (
              <p className="text-gray-700">{note.content.overview}</p>
            )}
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm text-gray-500 mb-1">Company</h4>
                {isEditing ? (
                  <Input
                    value={editedNote.content.companyDetails.company}
                    onChange={(e) =>
                      setEditedNote({
                        ...editedNote,
                        content: {
                          ...editedNote.content,
                          companyDetails: {
                            ...editedNote.content.companyDetails,
                            company: e.target.value,
                          },
                        },
                      })
                    }
                  />
                ) : (
                  <p className="font-medium">{note.content.companyDetails.company}</p>
                )}
              </div>
              <div>
                <h4 className="text-sm text-gray-500 mb-1">Location</h4>
                {isEditing ? (
                  <Input
                    value={editedNote.content.companyDetails.location}
                    onChange={(e) =>
                      setEditedNote({
                        ...editedNote,
                        content: {
                          ...editedNote.content,
                          companyDetails: {
                            ...editedNote.content.companyDetails,
                            location: e.target.value,
                          },
                        },
                      })
                    }
                  />
                ) : (
                  <p className="font-medium">{note.content.companyDetails.location}</p>
                )}
              </div>
              <div>
                <h4 className="text-sm text-gray-500 mb-1">Salary Range</h4>
                {isEditing ? (
                  <Input
                    value={editedNote.content.companyDetails.salaryRange}
                    onChange={(e) =>
                      setEditedNote({
                        ...editedNote,
                        content: {
                          ...editedNote.content,
                          companyDetails: {
                            ...editedNote.content.companyDetails,
                            salaryRange: e.target.value,
                          },
                        },
                      })
                    }
                  />
                ) : (
                  <p className="font-medium">{note.content.companyDetails.salaryRange}</p>
                )}
              </div>
              <div>
                <h4 className="text-sm text-gray-500 mb-1">Experience</h4>
                {isEditing ? (
                  <Input
                    value={editedNote.content.companyDetails.experience}
                    onChange={(e) =>
                      setEditedNote({
                        ...editedNote,
                        content: {
                          ...editedNote.content,
                          companyDetails: {
                            ...editedNote.content.companyDetails,
                            experience: e.target.value,
                          },
                        },
                      })
                    }
                  />
                ) : (
                  <p className="font-medium">{note.content.companyDetails.experience}</p>
                )}
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Key Responsibilities</h3>
            {isEditing ? (
              <div className="space-y-2">
                {editedNote.content.responsibilities.map((resp, index) => (
                  <div key={index} className="flex items-center">
                    <span className="mr-2">•</span>
                    <Input value={resp} onChange={(e) => updateResponsibility(index, e.target.value)} />
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setEditedNote({
                      ...editedNote,
                      content: {
                        ...editedNote.content,
                        responsibilities: [...editedNote.content.responsibilities, ""],
                      },
                    })
                  }
                >
                  Add Responsibility
                </Button>
              </div>
            ) : (
              <ul className="list-disc pl-5 space-y-1">
                {note.content.responsibilities.map((resp, index) => (
                  <li key={index} className="text-gray-700">
                    {resp}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

