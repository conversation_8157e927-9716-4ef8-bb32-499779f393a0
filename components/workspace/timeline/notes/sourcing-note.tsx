"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTit<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Edit, Save, X, Copy } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import type { Note, SourcingNote } from "../../types"

interface SourcingNoteTemplateProps {
  note: SourcingNote
  onSave?: (note: SourcingNote) => void
}

export function SourcingNoteTemplate({ note, onSave }: SourcingNoteTemplateProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedNote, setEditedNote] = useState<SourcingNote>(note)
  const [copySuccess, setCopySuccess] = useState(false)

  const handleSave = () => {
    setIsEditing(false)
    if (onSave) {
      onSave(editedNote)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditedNote(note)
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(note.booleanString)
    setCopySuccess(true)
    setTimeout(() => setCopySuccess(false), 2000)
  }

  return (
    <Card className="mt-6">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-xl font-semibold">{note.title}</CardTitle>
        {isEditing ? (
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleCancel}>
              <X className="h-4 w-4 mr-1" /> Cancel
            </Button>
            <Button size="sm" onClick={handleSave}>
              <Save className="h-4 w-4 mr-1" /> Save
            </Button>
          </div>
        ) : (
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-1" /> Edit
          </Button>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">Boolean Search String</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={copyToClipboard}
                className={copySuccess ? "bg-green-50 text-green-600" : ""}
              >
                <Copy className="h-4 w-4 mr-1" />
                {copySuccess ? "Copied!" : "Copy to Clipboard"}
              </Button>
            </div>
            {isEditing ? (
              <Textarea
                value={editedNote.booleanString}
                onChange={(e) =>
                  setEditedNote({
                    ...editedNote,
                    booleanString: e.target.value,
                  })
                }
                rows={4}
                className="font-mono text-sm"
              />
            ) : (
              <div className="bg-gray-50 p-3 rounded-md font-mono text-sm whitespace-pre-wrap">
                {note.booleanString}
              </div>
            )}
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Search Analysis</h3>
            {isEditing ? (
              <Textarea
                value={editedNote.analysis}
                onChange={(e) =>
                  setEditedNote({
                    ...editedNote,
                    analysis: e.target.value,
                  })
                }
                rows={3}
              />
            ) : (
              <p className="text-gray-700">{note.analysis}</p>
            )}
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Expected Results</h3>
            {isEditing ? (
              <Textarea
                value={editedNote.expectedResults}
                onChange={(e) =>
                  setEditedNote({
                    ...editedNote,
                    expectedResults: e.target.value,
                  })
                }
                rows={2}
              />
            ) : (
              <p className="text-gray-700">{note.expectedResults}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

