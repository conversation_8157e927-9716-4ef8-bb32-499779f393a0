"use client"
import { MeetingNoteTemplate } from "./meeting-note"
import { PostingNoteTemplate } from "./posting-note"
import { SourcingNoteTemplate } from "./sourcing-note"
import { ScreeningNoteTemplate } from "./screening-note"
import type { Note } from "../../types"

interface NoteRendererProps {
  note: Note
  onSave?: (note: Note) => void
}

export function NoteRenderer({ note, onSave }: NoteRendererProps) {
  console.log("Rendering note:", note)

  switch (note.type) {
    case "meeting":
      return <MeetingNoteTemplate note={note} onSave={onSave} />
    case "posting":
      return <PostingNoteTemplate note={note} onSave={onSave} />
    case "sourcing":
      return <SourcingNoteTemplate note={note} onSave={onSave} />
    case "screening":
      return <ScreeningNoteTemplate note={note} onSave={onSave} />
    default:
      return <div>Unknown note type</div>
  }
}

