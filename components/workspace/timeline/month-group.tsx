"use client"

import { useState } from "react"
import { ChevronDown, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import type { Objective, WorkspacePersona } from "../types"
import { ObjectiveCard } from "./objective-card"

interface MonthGroupProps {
  month: string
  year: number
  objectives: Objective[]
  programId: string
  activePersona: WorkspacePersona
  onTaskSelect?: (taskId: string, objectiveId: string, programId: string) => void
}

export function MonthGroup({ month, year, objectives, programId, activePersona, onTaskSelect }: MonthGroupProps) {
  const [isExpanded, setIsExpanded] = useState(true)

  // Calculate progress
  const completedObjectives = objectives.filter((obj) => obj.status === "completed").length
  const progressPercentage = objectives.length > 0 ? Math.round((completedObjectives / objectives.length) * 100) : 0

  return (
    <div className="border border-gray-200 rounded-md bg-white shadow-sm">
      <div className="p-3 flex items-center justify-between bg-gray-50 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => setIsExpanded(!isExpanded)}>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </Button>
          <h3 className="text-sm font-medium text-gray-800">
            {month} {year}
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">
            {completedObjectives}/{objectives.length} Objectives
          </span>
        </div>
      </div>

      {isExpanded && (
        <div className="p-3 space-y-3">
          <div>
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress:</span>
              <span>{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
          <div className="space-y-3">
            {objectives.map((objective) => (
              <ObjectiveCard
                key={objective.id}
                objective={objective}
                programId={programId}
                activePersona={activePersona}
                onTaskSelect={onTaskSelect}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

