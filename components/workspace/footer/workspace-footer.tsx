import type { WorkspaceItem } from "../types"

interface WorkspaceFooterProps {
  activeItem: WorkspaceItem | null
}

export function WorkspaceFooter({ activeItem }: WorkspaceFooterProps) {
  return (
    <div className="border-t border-border p-2 flex justify-between items-center bg-card">
      <div className="flex items-center space-x-2">{/* Left side is empty */}</div>
      <div className="flex items-center space-x-2">{/* Right side is now empty too */}</div>
    </div>
  )
}

