import {
  Users,
  ClipboardCheck,
  HeartPulse,
  GraduationCap,
  Building,
  LayoutDashboard,
  Calendar,
  BarChart2,
  Briefcase,
  Settings,
  Shield,
  UserCog,
  Building2,
  Home,
} from "lucide-react"
import type { LucideIcon } from "lucide-react"

export type NavigationCategory = "hcm" | "crm" | "settings"

export interface NavigationModule {
  id: string
  name: string
  icon: string
  persona: string
  children?: NavigationLink[]
}

export interface NavigationLink {
  id: string
  name: string
  icon: string
  path: string
  persona?: string
}

export const navigationModules: NavigationModule[] = [
  {
    id: "home",
    name: "Home",
    icon: "Home",
    persona: "all",
    children: [
      {
        id: "dashboard",
        name: "Dashboard",
        icon: "LayoutDashboard",
        path: "/home/<USER>",
        persona: "all",
      },
    ],
  },
  {
    id: "hcm",
    name: "<PERSON><PERSON>",
    icon: "Users",
    persona: "all",
    children: [
      {
        id: "recruiting",
        name: "Recruiting",
        icon: "Briefcase",
        path: "/hcm/recruiting",
        persona: "recruiter",
      },
      {
        id: "hr",
        name: "HR Management",
        icon: "ClipboardCheck",
        path: "/hcm/hr",
        persona: "hr-generalist",
      },
      {
        id: "benefits",
        name: "Benefits",
        icon: "HeartPulse",
        path: "/hcm/benefits",
        persona: "benefits",
      },
      {
        id: "learning",
        name: "Learning & Development",
        icon: "GraduationCap",
        path: "/hcm/learning",
        persona: "learning",
      },
    ],
  },
  {
    id: "crm",
    name: "CRM",
    icon: "Building",
    persona: "recruiter",
    children: [
      {
        id: "clients",
        name: "Clients",
        icon: "Building",
        path: "/crm/clients",
        persona: "recruiter",
      },
    ],
  },
  {
    id: "settings",
    name: "Settings",
    icon: "Settings",
    persona: "all",
    children: [
      {
        id: "organization",
        name: "Organization",
        icon: "Building2",
        path: "/settings/organization",
      },
      {
        id: "users",
        name: "Users",
        icon: "UserCog",
        path: "/settings/users",
      },
      {
        id: "roles",
        name: "Roles & Permissions",
        icon: "Shield",
        path: "/settings/roles",
      },
    ],
  },
]

export const workspaceViews: NavigationLink[] = [
  {
    id: "dashboard",
    name: "Dashboard",
    icon: "LayoutDashboard",
    path: "/dashboard",
  },
  {
    id: "timeline",
    name: "Timeline",
    icon: "Calendar",
    path: "/timeline",
  },
  {
    id: "reports",
    name: "Reports",
    icon: "BarChart2",
    path: "/reports",
  },
]

