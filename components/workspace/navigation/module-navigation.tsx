"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { navigationModules, type NavigationCategory } from "./navigation-data"
import type { WorkspacePersona } from "../types"

interface ModuleNavigationProps {
  activeCategory: NavigationCategory
  activePersona: WorkspacePersona
  setActivePersona: (persona: WorkspacePersona) => void
}

export function ModuleNavigation({ activeCategory, activePersona, setActivePersona }: ModuleNavigationProps) {
  // Get the active module based on the category
  const activeModule = navigationModules.find((module) => module.id === activeCategory)

  if (!activeModule || !activeModule.children) {
    return null
  }

  return (
    <div className="px-2 py-4 border-b border-white/10">
      <h3 className="px-2 text-xs font-semibold uppercase text-white/60 mb-2">{activeModule.name}</h3>
      <div className="space-y-1">
        {activeModule.children.map((child) => (
          <Button
            key={child.id}
            variant="ghost"
            className={cn(
              "w-full justify-start mb-1 font-medium text-white/80 hover:bg-white/10 hover:text-white",
              (activePersona === child.persona || (child.persona === undefined && activeCategory === "crm")) &&
                "bg-white/20 text-white",
            )}
            onClick={() => {
              if (child.persona) {
                setActivePersona(child.persona as WorkspacePersona)
              }

              // If this is the clients module, set the flag
              if (child.id === "clients" && window.setShowClientsModule) {
                window.setShowClientsModule(true)
              }
            }}
          >
            <child.icon className="h-4 w-4 mr-2" />
            <span>{child.name}</span>
          </Button>
        ))}
      </div>
    </div>
  )
}

