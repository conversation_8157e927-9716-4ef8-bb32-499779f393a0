"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { navigationModules, type NavigationCategory } from "./navigation-data"
import type { WorkspacePersona } from "../types"

interface TopNavigationProps {
  activeCategory: NavigationCategory
  setActiveCategory: (category: NavigationCategory) => void
  activePersona: WorkspacePersona
  setActivePersona: (persona: WorkspacePersona) => void
}

export function TopNavigation({
  activeCategory,
  setActiveCategory,
  activePersona,
  setActivePersona,
}: TopNavigationProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Filter modules based on user persona
  // In a real app, this would use actual user permissions
  const visibleModules = navigationModules.filter((module) => {
    // Show HCM to everyone
    if (module.id === "hcm") return true

    // For CRM, only show to recruiters
    if (module.id === "crm") {
      return activePersona === "recruiter"
    }

    return true
  })

  const handleCategoryClick = (category: NavigationCategory) => {
    setActiveCategory(category)

    // If switching to HCM, set the persona based on the first child
    if (category === "hcm") {
      const hcmModule = navigationModules.find((m) => m.id === "hcm")
      if (hcmModule?.children?.[0]?.persona) {
        setActivePersona(hcmModule.children[0].persona as WorkspacePersona)
      }
    }

    // If switching to CRM, set the persona to recruiter and show clients module
    if (category === "crm") {
      setActivePersona("recruiter")
      // Instead of navigating, we'll set a flag to show the clients module
      if (window.setShowClientsModule) {
        window.setShowClientsModule(true)
      }
    }

    setIsOpen(false)
  }

  return (
    <div className="flex items-center space-x-1 px-2">
      {visibleModules.map((module) => (
        <Button
          key={module.id}
          variant="ghost"
          className={cn(
            "text-white/80 hover:text-white hover:bg-white/10",
            activeCategory === module.id && "bg-white/20 text-white",
          )}
          onClick={() => handleCategoryClick(module.id as NavigationCategory)}
        >
          <module.icon className="h-4 w-4 mr-2" />
          {module.name}
        </Button>
      ))}
    </div>
  )
}

