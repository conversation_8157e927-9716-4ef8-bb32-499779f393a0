"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Download, Filter } from "lucide-react"
import type { WorkspacePersona } from "../types"

interface ReportsViewProps {
  activePersona: WorkspacePersona
  isClientModule?: boolean
}

export function ReportsView({ activePersona, isClientModule = false }: ReportsViewProps) {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-800">Reports</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="border-gray-200 text-gray-700">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" className="border-gray-200 text-gray-700">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">
              {isClientModule && "Client Placements"}
              {!isClientModule && activePersona === "recruiter" && "Time to Hire"}
              {!isClientModule && activePersona === "hr-generalist" && "Employee Turnover"}
              {!isClientModule && activePersona === "benefits" && "Benefits Utilization"}
              {!isClientModule && activePersona === "learning" && "Training Completion Rate"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-forest-600">
              {isClientModule && "37 placements"}
              {!isClientModule && activePersona === "recruiter" && "24 days"}
              {!isClientModule && activePersona === "hr-generalist" && "12%"}
              {!isClientModule && activePersona === "benefits" && "78%"}
              {!isClientModule && activePersona === "learning" && "83%"}
            </div>
            <p className="text-xs text-gray-500">
              {isClientModule && "+15% from last quarter"}
              {!isClientModule && activePersona === "recruiter" && "-15% from last quarter"}
              {!isClientModule && activePersona === "hr-generalist" && "+2% from last quarter"}
              {!isClientModule && activePersona === "benefits" && "+5% from last quarter"}
              {!isClientModule && activePersona === "learning" && "+8% from last quarter"}
            </p>
            <div className="h-32 mt-4 flex items-center justify-center bg-cream-50 rounded-md">
              <LineChart className="h-8 w-8 text-forest-600/50" />
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">
              {isClientModule && "Contract Types"}
              {!isClientModule && activePersona === "recruiter" && "Candidate Sources"}
              {!isClientModule && activePersona === "hr-generalist" && "Department Distribution"}
              {!isClientModule && activePersona === "benefits" && "Plan Enrollment"}
              {!isClientModule && activePersona === "learning" && "Training by Department"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-32 mt-4 flex items-center justify-center bg-cream-50 rounded-md">
              <PieChart className="h-8 w-8 text-forest-600/50" />
            </div>
            <div className="mt-4 text-xs text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>
                  {isClientModule && "Staffing Contracts"}
                  {!isClientModule && activePersona === "recruiter" && "LinkedIn"}
                  {!isClientModule && activePersona === "hr-generalist" && "Engineering"}
                  {!isClientModule && activePersona === "benefits" && "Health Plan"}
                  {!isClientModule && activePersona === "learning" && "Engineering"}
                </span>
                <span className="font-medium">
                  {isClientModule && "65%"}
                  {!isClientModule && activePersona === "recruiter" && "42%"}
                  {!isClientModule && activePersona === "hr-generalist" && "35%"}
                  {!isClientModule && activePersona === "benefits" && "92%"}
                  {!isClientModule && activePersona === "learning" && "45%"}
                </span>
              </div>
              <div className="flex justify-between">
                <span>
                  {isClientModule && "Consulting Contracts"}
                  {!isClientModule && activePersona === "recruiter" && "Referrals"}
                  {!isClientModule && activePersona === "hr-generalist" && "Sales"}
                  {!isClientModule && activePersona === "benefits" && "Dental Plan"}
                  {!isClientModule && activePersona === "learning" && "Sales"}
                </span>
                <span className="font-medium">
                  {isClientModule && "35%"}
                  {!isClientModule && activePersona === "recruiter" && "28%"}
                  {!isClientModule && activePersona === "hr-generalist" && "25%"}
                  {!isClientModule && activePersona === "benefits" && "85%"}
                  {!isClientModule && activePersona === "learning" && "30%"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">
              {isClientModule && "Client Revenue by Industry"}
              {!isClientModule && activePersona === "recruiter" && "Open Positions by Department"}
              {!isClientModule && activePersona === "hr-generalist" && "Compliance Status"}
              {!isClientModule && activePersona === "benefits" && "Cost per Employee"}
              {!isClientModule && activePersona === "learning" && "Skills Gap Analysis"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-32 mt-4 flex items-center justify-center bg-cream-50 rounded-md">
              <BarChart2 className="h-8 w-8 text-forest-600/50" />
            </div>
            <div className="mt-4 text-xs text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>
                  {isClientModule && "Technology"}
                  {!isClientModule && activePersona === "recruiter" && "Engineering"}
                  {!isClientModule && activePersona === "hr-generalist" && "Policy Compliance"}
                  {!isClientModule && activePersona === "benefits" && "2023"}
                  {!isClientModule && activePersona === "learning" && "Leadership"}
                </span>
                <span className="font-medium">
                  {isClientModule && "$845,300"}
                  {!isClientModule && activePersona === "recruiter" && "8 positions"}
                  {!isClientModule && activePersona === "hr-generalist" && "98%"}
                  {!isClientModule && activePersona === "benefits" && "$12,450"}
                  {!isClientModule && activePersona === "learning" && "Medium gap"}
                </span>
              </div>
              <div className="flex justify-between">
                <span>
                  {isClientModule && "Healthcare"}
                  {!isClientModule && activePersona === "recruiter" && "Sales"}
                  {!isClientModule && activePersona === "hr-generalist" && "Training Compliance"}
                  {!isClientModule && activePersona === "benefits" && "2022"}
                  {!isClientModule && activePersona === "learning" && "Technical"}
                </span>
                <span className="font-medium">
                  {isClientModule && "$425,750"}
                  {!isClientModule && activePersona === "recruiter" && "4 positions"}
                  {!isClientModule && activePersona === "hr-generalist" && "92%"}
                  {!isClientModule && activePersona === "benefits" && "$11,200"}
                  {!isClientModule && activePersona === "learning" && "High gap"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <Card className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">
              {isClientModule && "Client Revenue Trend"}
              {!isClientModule && activePersona === "recruiter" && "Recruitment Pipeline"}
              {!isClientModule && activePersona === "hr-generalist" && "Employee Lifecycle"}
              {!isClientModule && activePersona === "benefits" && "Benefits Cost Trend"}
              {!isClientModule && activePersona === "learning" && "Training Effectiveness"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-cream-50 rounded-md">
              <LineChart className="h-12 w-12 text-forest-600/50" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

