"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Plus, Building, Users, Briefcase, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import type { Client } from "../workspace/types"

interface ClientListProps {
  clients: Client[]
  onClientSelect: (client: Client) => void
  onCreateClient: () => void
}

export function ClientList({ clients, onClientSelect, onCreateClient }: ClientListProps) {
  const [searchQuery, setSearchQuery] = useState("")

  const filteredClients = searchQuery
    ? clients.filter(
        (client) =>
          client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          client.industry.toLowerCase().includes(searchQuery.toLowerCase()) ||
          client.contactPerson.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : clients

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-800">Clients</h1>
        <Button className="bg-forest-600 text-white hover:bg-forest-700" onClick={onCreateClient}>
          <Plus className="h-4 w-4 mr-2" />
          New Client
        </Button>
      </div>

      <div className="relative max-w-md mb-6">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search clients..."
          className="pl-8 border-gray-200"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredClients.map((client) => (
          <Card
            key={client.id}
            className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onClientSelect(client)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-md font-medium text-forest-600">{client.name}</CardTitle>
                <Badge variant="outline" className="bg-cream-100 text-forest-600 border-forest-200">
                  {client.industry}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Building className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{client.location || "No location specified"}</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{client.contactPerson}</span>
                </div>
                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                  <span>{client.openJobs || 0} open positions</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredClients.length === 0 && (
          <div className="col-span-full p-8 text-center text-gray-500 bg-cream-50 rounded-lg border border-dashed border-gray-200">
            No clients found. Try adjusting your search or add a new client.
          </div>
        )}
      </div>
    </div>
  )
}

