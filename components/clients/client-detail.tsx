"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Edit, Building, Mail, Phone, Calendar, Briefcase, Users, BarChart2, Plus } from "lucide-react"
import type { Client, Job } from "../workspace/types"

interface ClientDetailProps {
  client: Client
  jobs: Job[]
  onEditClient: (client: Client) => void
  onCreateJob: (clientId: string) => void
  onSelectJob: (job: Job) => void
}

export function ClientDetail({ client, jobs, onEditClient, onCreateJob, onSelectJob }: ClientDetailProps) {
  const clientJobs = jobs.filter((job) => job.clientId === client.id)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">{client.name}</h1>
          <div className="flex items-center mt-1">
            <Badge variant="outline" className="bg-cream-100 text-forest-600 border-forest-200 mr-2">
              {client.industry}
            </Badge>
            <span className="text-sm text-gray-500">Client since {client.createdAt.toLocaleDateString()}</span>
          </div>
        </div>
        <Button
          variant="outline"
          className="border-gray-200 text-gray-700 hover:border-forest-600 hover:text-forest-600"
          onClick={() => onEditClient(client)}
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Client
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border border-gray-200/50 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-gray-500" />
              <span>{client.contactPerson}</span>
            </div>
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-2 text-gray-500" />
              <a href={`mailto:${client.contactEmail}`} className="text-forest-600 hover:underline">
                {client.contactEmail}
              </a>
            </div>
            <div className="flex items-center">
              <Phone className="h-4 w-4 mr-2 text-gray-500" />
              <span>{client.contactPhone}</span>
            </div>
            <div className="flex items-center">
              <Building className="h-4 w-4 mr-2 text-gray-500" />
              <span>{client.location}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200/50 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Recruitment Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center">
              <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
              <span>{client.openJobs} open positions</span>
            </div>
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-gray-500" />
              <span>{client.totalPlacements} total placements</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
              <span>Last placement: {client.updatedAt.toLocaleDateString()}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200/50 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">{client.notes || "No notes available."}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="jobs" className="mt-6">
        <TabsList className="bg-cream-100 p-1">
          <TabsTrigger
            value="jobs"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Open Jobs
          </TabsTrigger>
          <TabsTrigger
            value="placements"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Placements
          </TabsTrigger>
          <TabsTrigger
            value="analytics"
            className="data-[state=active]:bg-white data-[state=active]:text-forest-600 data-[state=active]:shadow-sm text-gray-600 rounded-md"
          >
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="jobs" className="mt-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-800">Open Positions</h2>
            <Button className="bg-forest-600 text-white hover:bg-forest-700" onClick={() => onCreateJob(client.id)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Job
            </Button>
          </div>

          {clientJobs.length > 0 ? (
            <div className="space-y-3">
              {clientJobs.map((job) => (
                <Card
                  key={job.id}
                  className="border border-gray-200/50 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onSelectJob(job)}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-forest-600">{job.title}</h3>
                        <div className="flex items-center mt-1">
                          <Building className="h-3 w-3 mr-1 text-gray-500" />
                          <span className="text-sm text-gray-600 mr-3">{job.location}</span>
                          <Briefcase className="h-3 w-3 mr-1 text-gray-500" />
                          <span className="text-sm text-gray-600">{job.department}</span>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-cream-100 text-forest-600 border-forest-200">
                        {job.salary}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500 bg-cream-50 rounded-lg border border-dashed border-gray-200">
              No open jobs for this client. Create a new job to get started.
            </div>
          )}
        </TabsContent>

        <TabsContent value="placements" className="mt-4">
          <div className="p-8 text-center text-gray-500 bg-cream-50 rounded-lg border border-dashed border-gray-200">
            Placement history will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="mt-4">
          <div className="flex items-center justify-center h-64 bg-cream-50 rounded-lg border border-dashed border-gray-200">
            <div className="text-center text-gray-500">
              <BarChart2 className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <p>Client analytics will be displayed here.</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

