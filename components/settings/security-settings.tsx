"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Shield, Key, Lock, AlertTriangle } from "lucide-react"

export function SecuritySettings() {
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    passwordExpiry: true,
    passwordExpiryDays: 90,
    sessionTimeout: true,
    sessionTimeoutMinutes: 30,
    loginAttempts: true,
    maxLoginAttempts: 5,
  })

  const handleToggle = (setting: string) => {
    setSecuritySettings({
      ...securitySettings,
      [setting]: !securitySettings[setting as keyof typeof securitySettings],
    })
  }

  const handleInputChange = (setting: string, value: string) => {
    setSecuritySettings({
      ...securitySettings,
      [setting]: Number.parseInt(value) || 0,
    })
  }

  return (
    <div className="space-y-6">
      <Card className="border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-forest-600" />
            Authentication Settings
          </CardTitle>
          <CardDescription>Configure authentication and security settings for your organization.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Two-Factor Authentication</Label>
                <p className="text-sm text-muted-foreground">
                  Require two-factor authentication for all users in your organization.
                </p>
              </div>
              <Switch checked={securitySettings.twoFactorAuth} onCheckedChange={() => handleToggle("twoFactorAuth")} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Password Expiry</Label>
                <p className="text-sm text-muted-foreground">Force users to change their passwords periodically.</p>
              </div>
              <Switch
                checked={securitySettings.passwordExpiry}
                onCheckedChange={() => handleToggle("passwordExpiry")}
              />
            </div>

            {securitySettings.passwordExpiry && (
              <div className="ml-6 border-l-2 border-gray-200 pl-4 space-y-2">
                <Label htmlFor="passwordExpiryDays">Password expiry period (days)</Label>
                <Input
                  id="passwordExpiryDays"
                  type="number"
                  value={securitySettings.passwordExpiryDays}
                  onChange={(e) => handleInputChange("passwordExpiryDays", e.target.value)}
                  className="max-w-xs"
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Session Timeout</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically log out inactive users after a period of inactivity.
                </p>
              </div>
              <Switch
                checked={securitySettings.sessionTimeout}
                onCheckedChange={() => handleToggle("sessionTimeout")}
              />
            </div>

            {securitySettings.sessionTimeout && (
              <div className="ml-6 border-l-2 border-gray-200 pl-4 space-y-2">
                <Label htmlFor="sessionTimeoutMinutes">Session timeout (minutes)</Label>
                <Input
                  id="sessionTimeoutMinutes"
                  type="number"
                  value={securitySettings.sessionTimeoutMinutes}
                  onChange={(e) => handleInputChange("sessionTimeoutMinutes", e.target.value)}
                  className="max-w-xs"
                />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-base">Failed Login Attempts</Label>
                <p className="text-sm text-muted-foreground">Lock accounts after multiple failed login attempts.</p>
              </div>
              <Switch checked={securitySettings.loginAttempts} onCheckedChange={() => handleToggle("loginAttempts")} />
            </div>

            {securitySettings.loginAttempts && (
              <div className="ml-6 border-l-2 border-gray-200 pl-4 space-y-2">
                <Label htmlFor="maxLoginAttempts">Maximum login attempts</Label>
                <Input
                  id="maxLoginAttempts"
                  type="number"
                  value={securitySettings.maxLoginAttempts}
                  onChange={(e) => handleInputChange("maxLoginAttempts", e.target.value)}
                  className="max-w-xs"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lock className="h-5 w-5 mr-2 text-forest-600" />
            Password Policy
          </CardTitle>
          <CardDescription>Configure password requirements for your organization.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-cream-50 p-4 rounded-lg border border-gray-200/50">
              <h3 className="font-medium mb-2">Current Password Policy</h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-forest-600 mr-2"></div>
                  Minimum 8 characters
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-forest-600 mr-2"></div>
                  At least one uppercase letter
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-forest-600 mr-2"></div>
                  At least one lowercase letter
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-forest-600 mr-2"></div>
                  At least one number
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 rounded-full bg-forest-600 mr-2"></div>
                  At least one special character
                </li>
              </ul>
            </div>
            <Button variant="outline" className="border-gray-200 text-gray-700">
              <Key className="h-4 w-4 mr-2" />
              Edit Password Policy
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="border-gray-200/50 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-forest-600" />
            Security Audit Log
          </CardTitle>
          <CardDescription>View security-related events for your organization.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <p>Security audit logs would be displayed here.</p>
            <Button className="mt-4 bg-forest-600 hover:bg-forest-700 text-white">View Audit Logs</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

