-- Initial database schema for O6 application
-- This file creates all necessary tables for programs, objectives, tasks, notes, clients, and jobs

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the ORGANIZATIONS table (already exists from registration)
CREATE TABLE public.organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    industry TEXT,
    size TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create the PERSONAS table (already exists from registration)
CREATE TABLE public.personas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create the USERS table (already exists from registration)
CREATE TABLE public.users (
    id UUID PRIMARY KEY, -- This ID should match the ID from the auth.users table
    org_id UUID NOT NULL,
    persona_id UUID,
    first_name TEXT,
    last_name TEXT,
    email TEXT NOT NULL UNIQUE,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    CONSTRAINT fk_organization
        FOREIGN KEY(org_id)
        REFERENCES public.organizations(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_persona
        FOREIGN KEY(persona_id)
        REFERENCES public.personas(id)
        ON DELETE SET NULL
);

-- Create the PROGRAMS table
CREATE TABLE public.programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    quarter TEXT CHECK (quarter IN ('Q1', 'Q2', 'Q3', 'Q4')),
    year INTEGER NOT NULL,
    status TEXT CHECK (status IN ('planning', 'active', 'completed', 'on_hold')) DEFAULT 'planning',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    owner TEXT NOT NULL,
    persona TEXT CHECK (persona IN ('recruiter', 'hr-generalist', 'benefits', 'learning')) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_programs_organization
        FOREIGN KEY(org_id)
        REFERENCES public.organizations(id)
        ON DELETE CASCADE
);

-- Create the OBJECTIVES table
CREATE TABLE public.objectives (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    month INTEGER CHECK (month >= 0 AND month <= 11), -- 0-based month (January = 0)
    year INTEGER NOT NULL,
    status TEXT CHECK (status IN ('not_started', 'in_progress', 'completed')) DEFAULT 'not_started',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    owner TEXT NOT NULL,
    persona TEXT CHECK (persona IN ('recruiter', 'hr-generalist', 'benefits', 'learning')) NOT NULL,
    start_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_objectives_program
        FOREIGN KEY(program_id)
        REFERENCES public.programs(id)
        ON DELETE CASCADE
);

-- Create the TASKS table
CREATE TABLE public.tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    objective_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    due_date DATE,
    status TEXT CHECK (status IN ('pending', 'in_progress', 'completed')) DEFAULT 'pending',
    priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
    assigned_to TEXT NOT NULL,
    persona TEXT CHECK (persona IN ('recruiter', 'hr-generalist', 'benefits', 'learning')) NOT NULL,
    type TEXT CHECK (type IN ('meeting', 'posting', 'sourcing', 'screening', 'job_description', 'other')),
    note_id UUID,
    related_items TEXT[], -- Array of related item IDs
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_tasks_objective
        FOREIGN KEY(objective_id)
        REFERENCES public.objectives(id)
        ON DELETE CASCADE
);

-- Create the NOTES table
CREATE TABLE public.notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL,
    type TEXT CHECK (type IN ('meeting', 'posting', 'sourcing', 'screening')) NOT NULL,
    title TEXT,
    content JSONB NOT NULL, -- Store note-specific fields as JSON
    last_edited TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_notes_task
        FOREIGN KEY(task_id)
        REFERENCES public.tasks(id)
        ON DELETE CASCADE
);

-- Update tasks table to reference notes
ALTER TABLE public.tasks 
ADD CONSTRAINT fk_tasks_note
    FOREIGN KEY(note_id)
    REFERENCES public.notes(id)
    ON DELETE SET NULL;

-- Create the CLIENTS table
CREATE TABLE public.clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL,
    name TEXT NOT NULL,
    industry TEXT,
    location TEXT,
    contact_person TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    status TEXT CHECK (status IN ('active', 'inactive', 'lead', 'prospect')) DEFAULT 'lead',
    notes TEXT,
    open_jobs INTEGER DEFAULT 0,
    total_placements INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_clients_organization
        FOREIGN KEY(org_id)
        REFERENCES public.organizations(id)
        ON DELETE CASCADE
);

-- Create the JOBS table
CREATE TABLE public.jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL,
    client_id UUID, -- Optional reference to client
    title TEXT NOT NULL,
    content TEXT,
    tags TEXT[],
    department TEXT,
    location TEXT,
    salary TEXT,
    experience TEXT,
    persona TEXT CHECK (persona IN ('recruiter', 'hr-generalist', 'benefits', 'learning')) NOT NULL,
    type TEXT CHECK (type IN ('job_description', 'boolean_search', 'candidate_profile', 'meeting_notes')) DEFAULT 'job_description',
    status TEXT CHECK (status IN ('active', 'inactive', 'draft', 'archived')) DEFAULT 'draft',
    category TEXT,
    versions JSONB DEFAULT '[]'::jsonb, -- Store version history as JSON
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_jobs_organization
        FOREIGN KEY(org_id)
        REFERENCES public.organizations(id)
        ON DELETE CASCADE,
    
    CONSTRAINT fk_jobs_client
        FOREIGN KEY(client_id)
        REFERENCES public.clients(id)
        ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_programs_org_id ON public.programs(org_id);
CREATE INDEX idx_programs_persona ON public.programs(persona);
CREATE INDEX idx_objectives_program_id ON public.objectives(program_id);
CREATE INDEX idx_objectives_persona ON public.objectives(persona);
CREATE INDEX idx_tasks_objective_id ON public.tasks(objective_id);
CREATE INDEX idx_tasks_persona ON public.tasks(persona);
CREATE INDEX idx_notes_task_id ON public.notes(task_id);
CREATE INDEX idx_clients_org_id ON public.clients(org_id);
CREATE INDEX idx_jobs_org_id ON public.jobs(org_id);
CREATE INDEX idx_jobs_client_id ON public.jobs(client_id);
CREATE INDEX idx_jobs_persona ON public.jobs(persona);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_programs_updated_at BEFORE UPDATE ON public.programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_objectives_updated_at BEFORE UPDATE ON public.objectives FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON public.tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notes_updated_at BEFORE UPDATE ON public.notes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_jobs_updated_at BEFORE UPDATE ON public.jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default personas if they don't exist
INSERT INTO public.personas (name, description) VALUES 
('Owner', 'Organization owner with full access'),
('recruiter', 'Recruitment specialist focused on talent acquisition'),
('hr-generalist', 'HR professional managing employee lifecycle'),
('benefits', 'Benefits administrator managing employee benefits'),
('learning', 'Learning and development coordinator')
ON CONFLICT (name) DO NOTHING;
