-- Create resume evaluations table for storing resume evaluation data
-- This table stores resume evaluation results for tasks

CREATE TABLE public.resume_evaluations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL,
    candidate_name TEXT,
    resume_text TEXT NOT NULL,
    job_description TEXT NOT NULL,
    similarity_score DECIMAL(10,6),
    evaluation_result JSONB NOT NULL, -- Store the full evaluation JSON
    jury_flag BOOLEAN DEFAULT NULL,
    jury_critic TEXT DEFAULT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    CONSTRAINT fk_resume_evaluations_task
        FOREIGN KEY(task_id)
        REFERENCES public.tasks(id)
        ON DELETE CASCADE
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_resume_evaluations_task_id ON public.resume_evaluations(task_id);
CREATE INDEX idx_resume_evaluations_candidate_name ON public.resume_evaluations(candidate_name);
CREATE INDEX idx_resume_evaluations_created_at ON public.resume_evaluations(created_at);

-- <PERSON><PERSON> updated_at trigger
CREATE TRIGGER update_resume_evaluations_updated_at 
    BEFORE UPDATE ON public.resume_evaluations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
